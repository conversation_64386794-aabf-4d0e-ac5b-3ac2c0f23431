<template>
  <div class="dashboard-container">
    <!-- Header -->
    <div class="header">
      <div>
        <h1>Dashboard Inteligente: O que Realmente Importa</h1>
        <div class="header-subtitle">CaçaMultas - Revolução UX</div>
      </div>
    </div>

    <!-- Cards Principais -->
    <div class="main-cards">
      <div class="priority-card urgent">
        <div class="card-header">
          <div class="card-icon">🚨</div>
          <div class="card-title">Atenção Imediata</div>
        </div>
        <div class="card-value">R$ {{ formatCurrency(urgentValue) }}</div>
        <div class="card-subtitle">{{ urgentCount }} multas vencidas</div>
        <button class="card-action" @click="resolverUrgentes">⚡ Resolver Agora</button>
      </div>

      <div class="priority-card potential">
        <div class="card-header">
          <div class="card-icon">💰</div>
          <div class="card-title">Economia Potencial</div>
        </div>
        <div class="card-value">R$ {{ formatCurrency(potentialSavings) }}</div>
        <div class="card-subtitle">{{ contestableCount }} multas contestáveis</div>
        <button class="card-action" @click="contestarMultas">📋 Contestar</button>
      </div>

      <div class="priority-card realized">
        <div class="card-header">
          <div class="card-icon">📈</div>
          <div class="card-title">Economia Realizada</div>
        </div>
        <div class="card-value">R$ {{ formatCurrency(realizedSavings) }}</div>
        <div class="card-subtitle">{{ paymentRate }}% taxa de resolução</div>
        <button class="card-action" @click="verDetalhes">📊 Ver Detalhes</button>
      </div>

      <div class="priority-card prevented">
        <div class="card-header">
          <div class="card-icon">🛡️</div>
          <div class="card-title">Multas em Processo</div>
        </div>
        <div class="card-value">R$ {{ formatCurrency(inProcessValue) }}</div>
        <div class="card-subtitle">{{ inProcessCount }} em andamento</div>
        <button class="card-action" @click="verProcessos">🔍 Ver Status</button>
      </div>
    </div>

    <!-- Seção de Ações e Tendência -->
    <div class="actions-section">
      <!-- Lista de Ações Necessárias -->
      <div class="actions-list">
        <h2 class="section-title">Ações Necessárias</h2>
        
        <div 
          v-for="action in priorityActions" 
          :key="action.id"
          :class="['action-item', action.urgency + '-action']"
        >
          <div class="action-info">
            <div class="action-title">● {{ action.placa }}</div>
            <div class="action-detail">{{ action.infracao }} - R$ {{ formatCurrency(action.valor) }}</div>
            <div class="action-detail" v-if="action.observacao">{{ action.observacao }}</div>
          </div>
          <div :class="['action-status', 'status-' + action.urgency]">{{ action.prazo }}</div>
          <div class="action-buttons">
            <button class="action-button btn-resolve" @click="resolverMulta(action.id)">
              {{ action.urgency === 'urgent' ? 'Pagar' : 'Resolver' }}
            </button>
            <button 
              v-if="action.urgency === 'urgent'" 
              class="action-button btn-contest" 
              @click="contestarMulta(action.id)"
            >
              Contestar
            </button>
          </div>
        </div>
      </div>

      <!-- Gráfico de Tendência -->
      <div class="trend-section">
        <div class="trend-header">
          <h2 class="section-title">Tendência de Multas</h2>
          <div style="font-size: 14px; color: #7f8c8d;">Últimos 6 meses por valor e quantidade</div>
        </div>
        <div class="zoom-tip">
          Reduza o zoom para ver padrões de multas e identificar tendências!
        </div>
        <div class="chart-container">
          <canvas ref="trendChart"></canvas>
        </div>
        <div class="insight-box">
          💡 <strong>Sistema identifica padrões automaticamente e sugere ações!</strong>
        </div>
      </div>
    </div>

    <!-- Seção Inferior -->
    <div class="bottom-section">
      <!-- Padrões Detectados -->
      <div class="patterns-section">
        <h2 class="section-title">Padrões Detectados</h2>
        
        <div 
          v-for="pattern in detectedPatterns" 
          :key="pattern.id"
          class="pattern-item"
        >
          <div class="pattern-icon">{{ pattern.icon }}</div>
          <div class="pattern-text">
            <div class="pattern-title">{{ pattern.title }}</div>
            <div class="pattern-detail">{{ pattern.detail }}</div>
          </div>
          <button class="pattern-action" @click="executarAcao(pattern.action)">
            {{ pattern.actionText }}
          </button>
        </div>
      </div>

      <!-- Top Veículos -->
      <div class="vehicles-section">
        <h2 class="section-title">Top Veículos</h2>
        
        <div 
          v-for="(vehicle, index) in topVehicles" 
          :key="vehicle.placa"
          class="vehicle-item"
        >
          <div class="vehicle-info">
            <div class="vehicle-id">{{ index + 1 }} {{ vehicle.placa }}</div>
            <div class="vehicle-detail">{{ vehicle.count }} multas - R$ {{ formatCurrency(vehicle.valor) }}</div>
          </div>
          <div class="vehicle-actions">
            <button class="btn-generate" @click="gerenciarVeiculo(vehicle.placa)">Gerenciar</button>
          </div>
        </div>

        <div class="insight-footer">
          💡 <strong>Gestor identifica rapidamente quais veículos precisam de atenção!</strong>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Chart from 'chart.js/auto'

export default {
  name: 'DashboardInteligente',
  data() {
    return {
      // Dados baseados no CSV analisado
      urgentValue: 45280.50,
      urgentCount: 387,
      potentialSavings: 15420.30,
      contestableCount: 125,
      realizedSavings: 8950.75,
      paymentRate: 7.4,
      inProcessValue: 2659.12,
      inProcessCount: 4,
      
      priorityActions: [
        {
          id: 1,
          placa: 'AVP8001',
          infracao: 'Excesso de Peso',
          valor: 308.57,
          observacao: '6ª multa no mesmo local',
          prazo: 'Vence hoje',
          urgency: 'urgent'
        },
        {
          id: 2,
          placa: 'BWA0986',
          infracao: 'Excesso de Peso',
          valor: 372.41,
          prazo: '3 dias',
          urgency: 'warning'
        },
        {
          id: 3,
          placa: 'AEQ4456',
          infracao: 'Excesso de Peso',
          valor: 191.53,
          observacao: 'Pagamento confirmado',
          prazo: 'Resolvido',
          urgency: 'success'
        }
      ],
      
      detectedPatterns: [
        {
          id: 1,
          icon: '🔄',
          title: 'Múltiplas multas - Mesmo tipo',
          detail: '387 multas de excesso de peso - Padrão recorrente',
          action: 'contestar-lote',
          actionText: 'Contestar em Lote'
        },
        {
          id: 2,
          icon: '🚛',
          title: 'Veículos com alta incidência',
          detail: 'AVP8001 - 6 multas do mesmo tipo',
          action: 'treinamento',
          actionText: 'Programa de Treinamento'
        },
        {
          id: 3,
          icon: '⚠️',
          title: 'Órgão com alta incidência',
          detail: 'DER/PR - 95% das multas da frota',
          action: 'analise-rota',
          actionText: 'Analisar Rotas'
        },
        {
          id: 4,
          icon: '📍',
          title: 'Concentração geográfica',
          detail: 'Paraná - Região com maior incidência',
          action: 'otimizar-rota',
          actionText: 'Otimizar Rotas'
        }
      ],
      
      topVehicles: [
        { placa: 'AVP8001', count: 6, valor: 1247.85 },
        { placa: 'BWA0986', count: 4, valor: 892.33 },
        { placa: 'AEQ4456', count: 3, valor: 574.59 },
        { placa: 'ACD2283', count: 3, valor: 456.78 },
        { placa: 'AVF0141', count: 2, valor: 336.82 }
      ]
    }
  },
  
  mounted() {
    this.initChart()
    this.animateCards()
  },
  
  methods: {
    formatCurrency(value) {
      return new Intl.NumberFormat('pt-BR', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(value)
    },
    
    initChart() {
      const ctx = this.$refs.trendChart.getContext('2d')
      
      new Chart(ctx, {
        type: 'line',
        data: {
          labels: ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun'],
          datasets: [
            {
              label: 'Valor (R$)',
              data: [12500, 15200, 8900, 7800, 6200, 4500],
              borderColor: '#e74c3c',
              backgroundColor: 'rgba(231, 76, 60, 0.1)',
              yAxisID: 'y',
              tension: 0.4,
              fill: true
            },
            {
              label: 'Quantidade',
              data: [85, 92, 65, 58, 45, 32],
              borderColor: '#3498db',
              backgroundColor: 'rgba(52, 152, 219, 0.1)',
              yAxisID: 'y1',
              tension: 0.4,
              fill: false
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          interaction: {
            mode: 'index',
            intersect: false,
          },
          plugins: {
            legend: {
              position: 'top',
              labels: {
                usePointStyle: true,
                padding: 20,
                font: {
                  family: "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif"
                }
              }
            },
            tooltip: {
              callbacks: {
                label: function(context) {
                  if (context.datasetIndex === 0) {
                    return `Valor: R$ ${context.parsed.y.toLocaleString('pt-BR')}`
                  } else {
                    return `Quantidade: ${context.parsed.y} multas`
                  }
                }
              }
            }
          },
          scales: {
            x: {
              display: true,
              title: {
                display: true,
                text: 'Mês'
              },
              grid: {
                display: false
              }
            },
            y: {
              type: 'linear',
              display: true,
              position: 'left',
              title: {
                display: true,
                text: 'Valor (R$)',
                color: '#e74c3c'
              },
              ticks: {
                callback: function(value) {
                  return 'R$ ' + value.toLocaleString('pt-BR')
                },
                color: '#e74c3c'
              },
              grid: {
                color: 'rgba(231, 76, 60, 0.1)'
              }
            },
            y1: {
              type: 'linear',
              display: true,
              position: 'right',
              title: {
                display: true,
                text: 'Quantidade',
                color: '#3498db'
              },
              ticks: {
                color: '#3498db'
              },
              grid: {
                drawOnChartArea: false,
              },
            },
          }
        }
      })
    },
    
    animateCards() {
      this.$nextTick(() => {
        const cards = this.$el.querySelectorAll('.priority-card, .action-item, .pattern-item, .vehicle-item')
        cards.forEach((card, index) => {
          card.style.opacity = '0'
          card.style.transform = 'translateY(20px)'
          
          setTimeout(() => {
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease'
            card.style.opacity = '1'
            card.style.transform = 'translateY(0)'
          }, index * 100)
        })
      })
    },
    
    resolverUrgentes() {
      this.$emit('resolver-urgentes')
      alert('Resolvendo multas urgentes...')
    },
    
    contestarMultas() {
      this.$emit('contestar-multas')
      alert('Iniciando processo de contestação...')
    },
    
    verDetalhes() {
      this.$emit('ver-detalhes')
      alert('Abrindo detalhes da economia realizada...')
    },
    
    verProcessos() {
      this.$emit('ver-processos')
      alert('Visualizando multas em processo...')
    },
    
    resolverMulta(id) {
      this.$emit('resolver-multa', id)
      alert(`Resolvendo multa ${id}...`)
    },
    
    contestarMulta(id) {
      this.$emit('contestar-multa', id)
      alert(`Contestando multa ${id}...`)
    },
    
    gerenciarVeiculo(placa) {
      this.$emit('gerenciar-veiculo', placa)
      alert(`Gerenciando veículo ${placa}...`)
    },
    
    executarAcao(acao) {
      this.$emit('executar-acao', acao)
      alert(`Executando ação: ${acao}`)
    }
  }
}
</script>

<style scoped>
.dashboard-container {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  min-height: 100vh;
  padding: 20px;
  color: #333;
}

/* Header */
.header {
  background: #1e3c72;
  color: white;
  padding: 20px 30px;
  border-radius: 15px;
  margin-bottom: 25px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 8px 32px rgba(0,0,0,0.2);
}

.header h1 {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}

.header-subtitle {
  font-size: 14px;
  opacity: 0.9;
  margin-top: 5px;
}

/* Cards principais */
.main-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.priority-card {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  border-left: 6px solid;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
}

.priority-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0,0,0,0.15);
}

.priority-card.urgent {
  border-left-color: #e74c3c;
  background: linear-gradient(135deg, #fff 0%, #fdf2f2 100%);
}

.priority-card.potential {
  border-left-color: #27ae60;
  background: linear-gradient(135deg, #fff 0%, #f0f9f4 100%);
}

.priority-card.realized {
  border-left-color: #3498db;
  background: linear-gradient(135deg, #fff 0%, #f0f7ff 100%);
}

.priority-card.prevented {
  border-left-color: #f39c12;
  background: linear-gradient(135deg, #fff 0%, #fef9f0 100%);
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.card-icon {
  font-size: 24px;
  margin-right: 12px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.card-value {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 8px;
  color: #2c3e50;
}

.card-subtitle {
  font-size: 14px;
  color: #7f8c8d;
  margin-bottom: 15px;
}

.card-action {
  width: 100%;
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.urgent .card-action {
  background: #e74c3c;
  color: white;
}

.potential .card-action {
  background: #27ae60;
  color: white;
}

.realized .card-action {
  background: #3498db;
  color: white;
}

.prevented .card-action {
  background: #f39c12;
  color: white;
}

.card-action:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

/* Seção de ações */
.actions-section {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 30px;
  margin-bottom: 30px;
}

.actions-list {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 20px;
}

.action-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  margin-bottom: 15px;
  border-radius: 10px;
  border-left: 4px solid;
  transition: all 0.3s ease;
}

.action-item:hover {
  transform: translateX(5px);
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.action-item.urgent-action {
  background: #fdf2f2;
  border-left-color: #e74c3c;
}

.action-item.warning-action {
  background: #fef9f0;
  border-left-color: #f39c12;
}

.action-item.success-action {
  background: #f0f9f4;
  border-left-color: #27ae60;
}

.action-info {
  flex: 1;
}

.action-title {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 5px;
}

.action-detail {
  font-size: 12px;
  color: #7f8c8d;
}

.action-status {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  margin-right: 10px;
}

.status-urgent {
  background: #e74c3c;
  color: white;
}

.status-warning {
  background: #f39c12;
  color: white;
}

.status-success {
  background: #27ae60;
  color: white;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.action-button {
  padding: 8px 15px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-resolve {
  background: #e74c3c;
  color: white;
}

.btn-contest {
  background: #95a5a6;
  color: white;
}

.btn-resolve:hover, .btn-contest:hover {
  transform: translateY(-2px);
}

/* Gráfico de tendência */
.trend-section {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  position: relative;
}

.trend-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-container {
  position: relative;
  height: 300px;
  margin-bottom: 20px;
}

.insight-box {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  padding: 15px;
  border-radius: 10px;
  border-left: 4px solid #2196f3;
  font-size: 14px;
  color: #1565c0;
}

.zoom-tip {
  position: absolute;
  top: 20px;
  right: 20px;
  background: #2196f3;
  color: white;
  padding: 10px 15px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 600;
  z-index: 10;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

/* Seção inferior */
.bottom-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
}

.patterns-section {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

.pattern-item {
  display: flex;
  align-items: center;
  padding: 15px;
  margin-bottom: 15px;
  border-radius: 10px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-left: 4px solid #ffc107;
}

.pattern-icon {
  font-size: 20px;
  margin-right: 15px;
}

.pattern-text {
  flex: 1;
}

.pattern-title {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 5px;
}

.pattern-detail {
  font-size: 12px;
  color: #6c757d;
}

.pattern-action {
  padding: 8px 15px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.pattern-action:hover {
  background: #0056b3;
  transform: translateY(-2px);
}

.vehicles-section {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

.vehicle-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  margin-bottom: 15px;
  border-radius: 10px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-left: 4px solid #dc3545;
}

.vehicle-info {
  flex: 1;
}

.vehicle-id {
  font-weight: 700;
  color: #dc3545;
  font-size: 16px;
  margin-bottom: 5px;
}

.vehicle-detail {
  font-size: 12px;
  color: #6c757d;
}

.vehicle-actions {
  display: flex;
  gap: 10px;
}

.btn-generate {
  padding: 8px 15px;
  background: #17a2b8;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-generate:hover {
  background: #138496;
  transform: translateY(-2px);
}

.insight-footer {
  background: #e8f4fd;
  padding: 15px;
  border-radius: 10px;
  margin-top: 20px;
  font-size: 14px;
  color: #1565c0;
}

/* Responsividade */
@media (max-width: 1200px) {
  .actions-section {
    grid-template-columns: 1fr;
  }
  .bottom-section {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .main-cards {
    grid-template-columns: 1fr;
  }
  .header {
    flex-direction: column;
    text-align: center;
  }
  .dashboard-container {
    padding: 15px;
  }
}
</style>
