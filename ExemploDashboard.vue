<template>
  <div id="app">
    <DashboardInteligente 
      @resolver-urgentes="handleResolverUrgentes"
      @contestar-multas="handleContestarMultas"
      @ver-detalhes="handleVerDetalhes"
      @ver-processos="handleVerProcessos"
      @resolver-multa="handleResolverMulta"
      @contestar-multa="handleContestarMulta"
      @gerenciar-veiculo="handleGerenciarVeiculo"
      @executar-acao="handleExecutarAcao"
    />
  </div>
</template>

<script>
import DashboardInteligente from './DashboardInteligente.vue'

export default {
  name: 'ExemploDashboard',
  components: {
    DashboardInteligente
  },
  methods: {
    handleResolverUrgentes() {
      console.log('Resolvendo multas urgentes...')
      // Implementar lógica para resolver multas urgentes
      // Exemplo: chamar API, navegar para página específica, etc.
    },
    
    handleContestarMultas() {
      console.log('Iniciando processo de contestação...')
      // Implementar lógica para contestar multas
    },
    
    handleVerDetalhes() {
      console.log('Visualizando detalhes da economia...')
      // Implementar navegação para página de detalhes
    },
    
    handleVerProcessos() {
      console.log('Visualizando multas em processo...')
      // Implementar visualização de processos
    },
    
    handleResolverMulta(multaId) {
      console.log(`Resolvendo multa ${multaId}`)
      // Implementar resolução de multa específica
    },
    
    handleContestarMulta(multaId) {
      console.log(`Contestando multa ${multaId}`)
      // Implementar contestação de multa específica
    },
    
    handleGerenciarVeiculo(placa) {
      console.log(`Gerenciando veículo ${placa}`)
      // Implementar gerenciamento de veículo específico
    },
    
    handleExecutarAcao(acao) {
      console.log(`Executando ação: ${acao}`)
      // Implementar ação específica baseada no padrão detectado
    }
  }
}
</script>

<style>
#app {
  margin: 0;
  padding: 0;
}

/* Reset global para garantir consistência */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: #f5f5f5;
}
</style>
