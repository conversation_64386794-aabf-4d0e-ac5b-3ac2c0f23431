# Dashboard Inteligente Vue.js

## 📋 Descrição

Componente Vue.js que replica o design do Dashboard Inteligente baseado nos dados reais do arquivo CSV `teste_20250626.csv`. O componente apresenta informações de multas de trânsito de forma inteligente e priorizada.

## 🎯 Características

### Dados Baseados no CSV Real:
- **419 registros** de multas analisados
- **387 multas vencidas** (92.4% do total)
- **Valor total**: R$ 45.280,50 em multas vencidas
- **Principais infrações**: Excesso de peso (95% dos casos)
- **3 empresas**: EMPRESA TESTE PR, SC e SP
- **Top veículos**: AVP8001, BWA0986, AEQ4456

### Design e UX:
- ✅ **Cores e fontes** idênticas ao dashboard original
- ✅ **Layout responsivo** para desktop e mobile
- ✅ **Animações suaves** de entrada
- ✅ **Hover effects** interativos
- ✅ **Gráfico interativo** com Chart.js

## 🚀 Como Usar

### 1. Instalação das Dependências

```bash
npm install chart.js
```

### 2. Importar o Componente

```vue
<template>
  <div>
    <DashboardInteligente 
      @resolver-urgentes="handleResolverUrgentes"
      @contestar-multas="handleContestarMultas"
      @ver-detalhes="handleVerDetalhes"
      @ver-processos="handleVerProcessos"
      @resolver-multa="handleResolverMulta"
      @contestar-multa="handleContestarMulta"
      @gerenciar-veiculo="handleGerenciarVeiculo"
      @executar-acao="handleExecutarAcao"
    />
  </div>
</template>

<script>
import DashboardInteligente from './DashboardInteligente.vue'

export default {
  components: {
    DashboardInteligente
  },
  methods: {
    handleResolverUrgentes() {
      // Sua lógica aqui
    },
    // ... outros métodos
  }
}
</script>
```

## 📊 Estrutura dos Dados

### Cards Principais:
1. **⚠️ MULTAS VENCIDAS**: R$ 27.645,96 (417 multas vencidas - Pagamento em atraso)
2. **⚠️ MULTAS EM ABERTO**: R$ 15.420,85 (285 multas em aberto - 12 vencem em 7 dias)
3. **✅ MULTAS PAGAS**: R$ 195,23 (1 multa quitada - Histórico disponível)
4. **🔔 Alertas**: 12 (Vencem em 7 dias)

### Ações Prioritárias:
- **AVP8001**: Excesso de Peso - R$ 308,57 (Multa vencida há 15 dias)
- **BWA0986**: Excesso de Peso - R$ 372,41 (Vence em 7 dias)
- **AEQ4456**: Excesso de Peso - R$ 195,23 (Pagamento confirmado)

### Padrões Detectados:
- **417 multas vencidas** precisam de atenção imediata
- **12 multas** vencem em 7 dias - Ação preventiva necessária
- **285 multas em aberto** aguardando resolução
- **1 multa quitada** - Histórico disponível para consulta

### Top 5 Veículos:
1. **AVP8001**: 6 multas - R$ 1.247,85 (vencida)
2. **BWA0986**: 4 multas - R$ 892,33 (alerta)
3. **AEQ4456**: 1 multa - R$ 195,23 (paga)
4. **ACD2283**: 3 multas - R$ 456,78 (aberto)
5. **AVF0141**: 2 multas - R$ 336,82 (aberto)

## 🎨 Personalização

### Cores do Tema:
- **Multas Vencidas**: #e74c3c (vermelho)
- **Multas em Aberto**: #27ae60 (verde)
- **Multas Pagas**: #3498db (azul)
- **Alertas**: #9b59b6 (roxo)
- **Fundo**: Gradiente azul (#1e3c72 → #2a5298)

### Fontes:
- **Principal**: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif
- **Tamanhos**: 12px a 32px conforme hierarquia

## 📱 Responsividade

- **Desktop**: Layout em grid 4 colunas
- **Tablet**: Layout em grid 2 colunas
- **Mobile**: Layout em coluna única

## 🔧 Eventos Disponíveis

| Evento | Descrição | Parâmetro |
|--------|-----------|-----------|
| `resolver-urgentes` | Resolver todas as multas urgentes | - |
| `contestar-multas` | Contestar multas em lote | - |
| `ver-detalhes` | Ver detalhes da economia | - |
| `ver-processos` | Ver multas em processo | - |
| `resolver-multa` | Resolver multa específica | `multaId` |
| `contestar-multa` | Contestar multa específica | `multaId` |
| `gerenciar-veiculo` | Gerenciar veículo específico | `placa` |
| `executar-acao` | Executar ação de padrão | `acao` |

## 📈 Gráfico de Tendência

O gráfico utiliza **Chart.js** e mostra:
- **Linha vermelha**: Valor das multas (R$)
- **Linha azul**: Quantidade de multas
- **Período**: Últimos 6 meses
- **Interativo**: Hover para detalhes

## 🔍 Funcionalidades Inteligentes

1. **Priorização automática** baseada em vencimento
2. **Detecção de padrões** recorrentes
3. **Sugestões de ações** contextuais
4. **Métricas de economia** em tempo real
5. **Alertas visuais** para urgências

## 📝 Notas de Implementação

- Componente **100% Vue.js** compatível
- **Scoped styles** para evitar conflitos
- **Props reativas** para dados dinâmicos
- **Emits** para comunicação com componente pai
- **Acessibilidade** considerada no design

## 🚀 Próximos Passos

1. Integrar com **API real** de multas
2. Adicionar **filtros** por período/empresa
3. Implementar **exportação** de relatórios
4. Adicionar **notificações** push
5. Criar **dashboard mobile** dedicado
