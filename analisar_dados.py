import csv
from collections import defaultdict, Counter
from datetime import datetime

def analisar_csv():
    dados = []
    
    # Ler o arquivo CSV
    with open('teste_20250626.csv', 'r', encoding='utf-8') as file:
        reader = csv.reader(file)
        for row in reader:
            if len(row) >= 24:  # Garan<PERSON>r que a linha tem dados suficientes
                dados.append(row)
    
    print(f"=== ANÁLISE DOS DADOS ===")
    print(f"Total de registros: {len(dados)}")
    
    # Analisar placas
    placas = [row[2] for row in dados if row[2]]
    placas_count = Counter(placas)
    print(f"Total de placas únicas: {len(set(placas))}")
    
    # Analisar status
    status = [row[16] for row in dados if len(row) > 16]
    status_count = Counter(status)
    print(f"\n=== STATUS DAS MULTAS ===")
    for st, count in status_count.items():
        print(f"{st}: {count}")
    
    # Analisar valores
    valores = []
    for row in dados:
        if len(row) > 13 and row[13]:
            try:
                valor = float(row[13].replace(',', '.').replace('"', ''))
                valores.append(valor)
            except:
                pass
    
    total_valor = sum(valores)
    print(f"\n=== VALORES ===")
    print(f"Valor total: R$ {total_valor:,.2f}")
    print(f"Valor médio por multa: R$ {total_valor/len(valores):,.2f}")
    
    # Top 5 placas
    print(f"\n=== TOP 5 PLACAS COM MAIS MULTAS ===")
    for placa, count in placas_count.most_common(5):
        # Calcular valor total da placa
        valor_placa = 0
        for row in dados:
            if len(row) > 13 and row[2] == placa and row[13]:
                try:
                    valor = float(row[13].replace(',', '.').replace('"', ''))
                    valor_placa += valor
                except:
                    pass
        print(f"{placa}: {count} multas - R$ {valor_placa:,.2f}")
    
    # Analisar infrações
    infracoes = [row[6] for row in dados if len(row) > 6 and row[6]]
    infracoes_count = Counter(infracoes)
    print(f"\n=== INFRAÇÕES MAIS COMUNS ===")
    for infracao, count in infracoes_count.most_common(3):
        print(f"{infracao}: {count}")
    
    # Analisar empresas
    empresas = [row[23] for row in dados if len(row) > 23 and row[23]]
    empresas_count = Counter(empresas)
    print(f"\n=== EMPRESAS ===")
    for empresa, count in empresas_count.items():
        print(f"{empresa}: {count} multas")
    
    # Calcular métricas para o dashboard
    vencidas = sum(1 for row in dados if len(row) > 16 and row[16] == 'vencido')
    pagas = sum(1 for row in dados if len(row) > 16 and row[16] == 'pago')
    
    print(f"\n=== MÉTRICAS PARA DASHBOARD ===")
    print(f"Multas vencidas: {vencidas}")
    print(f"Multas pagas: {pagas}")
    print(f"Taxa de pagamento: {(pagas/(vencidas+pagas)*100):.1f}%")
    
    # Valor por status
    valor_vencido = 0
    valor_pago = 0
    for row in dados:
        if len(row) > 16 and len(row) > 13 and row[13]:
            try:
                valor = float(row[13].replace(',', '.').replace('"', ''))
                if row[16] == 'vencido':
                    valor_vencido += valor
                elif row[16] == 'pago':
                    valor_pago += valor
            except:
                pass
    
    print(f"Valor em multas vencidas: R$ {valor_vencido:,.2f}")
    print(f"Valor em multas pagas: R$ {valor_pago:,.2f}")

if __name__ == "__main__":
    analisar_csv()
