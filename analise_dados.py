import pandas as pd
import numpy as np
from datetime import datetime

# Ler o CSV
df = pd.read_csv('teste_20250626.csv', header=None)

# Definir nomes das colunas baseado na análise
columns = [
    'numero_auto', 'cnpj', 'placa', 'col3', 'col4', 'col5', 'infracao', 'col7', 
    'orgao_autuador', 'orgao_autuador2', 'data_infracao', 'data_infracao2', 
    'data_vencimento_original', 'valor_original', 'col14', 'pontos', 'status', 
    'data_consulta', 'data_consulta2', 'valor_atual', 'data_vencimento_atual', 
    'col21', 'col22', 'empresa', 'cnpj2'
]

df.columns = columns

print('=== ANÁLISE DOS DADOS REAIS ===')
print(f'Total de registros: {len(df)}')
print(f'CNPJ único: {df["cnpj"].unique()[0]}')
print(f'Empresa: {df["empresa"].unique()[0]}')
print()

print('=== ÓRGÃOS AUTUADORES ===')
print(df['orgao_autuador'].value_counts())
print()

print('=== TIPOS DE INFRAÇÃO ===')
print(df['infracao'].value_counts())
print()

print('=== TOP 10 VEÍCULOS COM MAIS MULTAS ===')
print(df['placa'].value_counts().head(10))
print()

print('=== VALORES ===')
# Limpar valores monetários
df['valor_atual_clean'] = df['valor_atual'].astype(str).str.replace('"', '').str.replace(',', '.')
df['valor_atual_num'] = pd.to_numeric(df['valor_atual_clean'], errors='coerce')

print(f'Valor total das multas: R$ {df["valor_atual_num"].sum():,.2f}')
print(f'Valor médio por multa: R$ {df["valor_atual_num"].mean():,.2f}')
print(f'Maior multa: R$ {df["valor_atual_num"].max():,.2f}')
print(f'Menor multa: R$ {df["valor_atual_num"].min():,.2f}')
print()

print('=== STATUS DAS MULTAS ===')
print(df['status'].value_counts())
print()

print('=== ANOS DAS INFRAÇÕES ===')
df['data_infracao_clean'] = pd.to_datetime(df['data_infracao'], errors='coerce')
df['ano_infracao'] = df['data_infracao_clean'].dt.year
print(df['ano_infracao'].value_counts().sort_index())
print()

print('=== ANÁLISE POR VEÍCULO (TOP 5) ===')
top_veiculos = df['placa'].value_counts().head(5)
for placa in top_veiculos.index:
    veiculo_data = df[df['placa'] == placa]
    valor_total = veiculo_data['valor_atual_num'].sum()
    print(f'{placa}: {len(veiculo_data)} multas - R$ {valor_total:,.2f}')
print()

print('=== DISTRIBUIÇÃO POR ÓRGÃO (%) ===')
orgao_counts = df['orgao_autuador'].value_counts()
for orgao, count in orgao_counts.items():
    pct = (count / len(df)) * 100
    print(f'{orgao}: {count} multas ({pct:.1f}%)')
print()

print('=== MULTAS POR ANO ===')
ano_counts = df['ano_infracao'].value_counts().sort_index()
for ano, count in ano_counts.items():
    if pd.notna(ano):
        valor_ano = df[df['ano_infracao'] == ano]['valor_atual_num'].sum()
        print(f'{int(ano)}: {count} multas - R$ {valor_ano:,.2f}')
