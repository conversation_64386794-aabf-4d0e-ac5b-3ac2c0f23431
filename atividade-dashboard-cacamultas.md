# Atividade: Dashboard CaçaMultas Embarcador

## 1. Contexto

O produto **CaçaMultas Embarcador** é uma solução de captura e gestão de multas vinculadas ao CNPJ **78015690000140** da **EMPRESA TESTE PR**. O sistema identifica principalmente infrações de **excesso de peso** (98% dos casos), com dados históricos de 2009 a 2024. Atualmente, o sistema apresenta os dados em formato de tabela simples, dificultando a análise estratégica e tomada de decisões pelos gestores.

### Dados Reais do Cliente
- **CNPJ Principal**: 78015690000140 (EMPRESA TESTE PR)
- **Total de Multas**: 418 registros
- **Valor Total**: R$ 27.645,96
- **Valor Médio**: R$ 297,27 por multa
- **Status**: 417 vencidas, 1 paga
- **Órgãos**: DER/PR (98,6%), DNIT (1,2%), DER/SP (0,2%)
- **Período**: 2009-2024 (pico em 2011 com 170 multas)
- **Veículos Críticos**: AVF0141 (12 multas), AIU8401 (12 multas), AKY0874 (11 multas)

A necessidade de um dashboard surge da demanda por:
- Gestão eficiente das 417 multas vencidas (R$ 27.645,96)
- Identificação de padrões nos veículos mais multados
- Análise da concentração em infrações de excesso de peso
- Facilitar a tomada de decisões estratégicas para a frota
- Otimizar o processo de gestão e negociação de multas

## 2. Objetivo

Desenvolver um dashboard único e completo que integre visão executiva com análises detalhadas para a EMPRESA TESTE PR, permitindo gestão eficiente das 418 multas (R$ 27.645,96) com foco nas 417 vencidas, proporcionando insights acionáveis para redução de custos e otimização da frota.

### Objetivos Específicos:
- Criar um dashboard unificado com métricas executivas e análises detalhadas
- Implementar filtros interativos por período, órgão e veículo
- Facilitar a identificação dos veículos mais críticos (AVF0141, AIU8401, AKY0874)
- Proporcionar análises temporais (2009-2024) e por órgão autuador
- Otimizar a gestão das multas vencidas com insights acionáveis

## 3. User Story

**Como** gestor de frota/compliance da EMPRESA TESTE PR (CNPJ: 78015690000140),
**Eu quero** visualizar um dashboard com informações consolidadas sobre as 418 multas da empresa, focando nas 417 vencidas,
**Para que** eu possa tomar decisões estratégicas sobre os R$ 27.645,96 em multas, identificar padrões nos veículos mais multados e gerenciar eficientemente o processo de negociação e regularização.

### Critérios de Aceitação da User Story:
- Visualizar resumo executivo das 417 multas vencidas (R$ 27.645,96)
- Identificar os veículos mais críticos (AVF0141, AIU8401, AKY0874)
- Analisar concentração em infrações de excesso de peso (98% dos casos)
- Visualizar distribuição por órgão autuador (DER/PR dominante)
- Acompanhar evolução histórica (2009-2024)
- Acessar detalhes específicos de cada multa para ação

## 4. Requisitos Funcionais (RF)

### RF01 - Cards de Métricas Principais
- **Descrição**: Exibir cards com indicadores principais da EMPRESA TESTE PR
- **Detalhes**:
  - Total de multas vencidas: 417 (R$ 27.645,96)
  - Multa paga: 1 registro
  - Valor médio por multa: R$ 297,27
  - Período de análise: 2009-2024

### RF02 - Análise de Multas por Status
- **Descrição**: Identificar e destacar o status das 418 multas
- **Detalhes**:
  - Foco nas 417 multas vencidas que precisam de ação
  - Destaque para a única multa paga como referência
  - Cálculo de potencial de negociação/parcelamento
  - Priorização por valor e antiguidade

### RF03 - Distribuição por Órgão Autuador
- **Descrição**: Visualizar distribuição das multas por órgão responsável
- **Detalhes**:
  - DER/PR: 412 multas (98,6%) - órgão dominante
  - DNIT: 5 multas (1,2%)
  - DER/SP: 1 multa (0,2%)
  - Valores totais por órgão

### RF04 - Análise por Veículo
- **Descrição**: Identificar veículos com maior incidência de multas
- **Detalhes**:
  - Top veículos: AVF0141 (12 multas), AIU8401 (12 multas), AKY0874 (11 multas)
  - Valores totais por veículo
  - Identificação de veículos problemáticos que precisam de ação

### RF05 - Tendências Temporais
- **Descrição**: Mostrar evolução das multas de 2009 a 2024
- **Detalhes**:
  - Pico em 2011 com 170 multas
  - Redução significativa após 2013
  - Gráfico de linha com quantidade e valores anuais
  - Identificação de padrões históricos

### RF06 - Análises Detalhadas Integradas
- **Descrição**: Seção completa de análises com filtros interativos
- **Detalhes**:
  - Filtros por período (2009-2024), órgão (DER/PR, DNIT, DER/SP) e veículo
  - Gráfico de evolução temporal com destaque para pico de 2011
  - Top 10 veículos mais multados com valores
  - Distribuição de valores das multas
  - Status detalhado (417 vencidas vs 1 paga)

### RF07 - Detecção de Padrões Específicos
- **Descrição**: Identificar padrões baseados nos dados reais
- **Detalhes**:
  - Concentração em excesso de peso (98% das multas)
  - Dominância do DER/PR (98,6% dos casos)
  - Pico histórico em 2011 e redução pós-2013
  - Veículos críticos que precisam de ação

### RF08 - Ações Prioritárias
- **Descrição**: Lista de ações baseada nos dados reais
- **Detalhes**:
  - Foco nos veículos AVF0141 e AIU8401 (12 multas cada)
  - Gestão das 417 multas vencidas
  - Negociação em lote com DER/PR
  - Análise de padrões para prevenção

### RF09 - Filtros e Interatividade
- **Descrição**: Permitir filtragem e drill-down nos dados
- **Detalhes**:
  - Filtros por período, órgão, CNPJ, tipo de infração
  - Capacidade de drill-down em gráficos
  - Exportação de dados filtrados

### RF10 - Responsividade
- **Descrição**: Dashboard deve ser responsivo para diferentes dispositivos
- **Detalhes**:
  - Adaptação para desktop, tablet e mobile
  - Manutenção da usabilidade em todas as resoluções

## 5. QA - Critérios de Aceite

### CA01 - Performance
- [ ] Dashboard deve carregar em menos de 3 segundos
- [ ] Filtros devem responder em menos de 1 segundo
- [ ] Suporte a pelo menos 10.000 registros de multas

### CA02 - Usabilidade
- [ ] Interface intuitiva, sem necessidade de treinamento extensivo
- [ ] Navegação clara entre diferentes seções
- [ ] Tooltips explicativos em elementos complexos
- [ ] Feedback visual para ações do usuário

### CA03 - Precisão dos Dados
- [ ] Cálculos de economia devem estar corretos (validação manual)
- [ ] Datas de vencimento calculadas corretamente
- [ ] Totalizadores devem bater com soma individual
- [ ] Filtros devem retornar dados corretos

### CA04 - Responsividade
- [ ] Layout adaptável para resoluções 1920x1080, 1366x768, 768x1024
- [ ] Elementos clicáveis com tamanho mínimo de 44px em mobile
- [ ] Texto legível em todas as resoluções

### CA05 - Acessibilidade
- [ ] Contraste adequado (WCAG 2.1 AA)
- [ ] Navegação por teclado funcional
- [ ] Textos alternativos em gráficos
- [ ] Suporte a leitores de tela

### CA06 - Funcionalidades Específicas
- [ ] Cards de métricas atualizados em tempo real
- [ ] Gráficos interativos com drill-down
- [ ] Exportação de dados em Excel/PDF
- [ ] Alertas visuais para multas urgentes
- [ ] Cálculo automático de descontos por pagamento antecipado

## 6. Exemplos Prototipados

Os protótipos a seguir demonstram a implementação visual dos requisitos funcionais:

### 6.1 Layout Principal
*[Protótipo será criado em arquivo HTML separado]*

### 6.2 Cards de Métricas
*[Implementação detalhada dos cards principais]*

### 6.3 Gráficos e Visualizações
*[Exemplos de gráficos interativos]*

### 6.4 Seção de Ações Necessárias
*[Interface para gestão de ações prioritárias]*

---

**Próximos Passos:**
1. Validação dos requisitos com stakeholders
2. Criação dos protótipos interativos
3. Desenvolvimento da primeira versão
4. Testes de usabilidade
5. Implementação em produção

**Estimativa de Desenvolvimento:** 3-4 sprints (6-8 semanas)
**Prioridade:** Alta
**Complexidade:** Média-Alta
