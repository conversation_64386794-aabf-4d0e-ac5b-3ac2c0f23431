<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Inteligente: O que Realmente Importa - CaçaMultas</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        /* Header */
        .header {
            background: #1e3c72;
            color: white;
            padding: 20px 30px;
            border-radius: 15px;
            margin-bottom: 25px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 8px 32px rgba(0,0,0,0.2);
        }

        .header h1 {
            font-size: 24px;
            font-weight: 600;
        }

        .header-subtitle {
            font-size: 14px;
            opacity: 0.9;
            margin-top: 5px;
        }

        /* Cards principais */
        .main-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .priority-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            border-left: 6px solid;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .priority-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.15);
        }

        .priority-card.urgent {
            border-left-color: #e74c3c;
            background: linear-gradient(135deg, #fff 0%, #fdf2f2 100%);
        }

        .priority-card.potential {
            border-left-color: #27ae60;
            background: linear-gradient(135deg, #fff 0%, #f0f9f4 100%);
        }

        .priority-card.realized {
            border-left-color: #3498db;
            background: linear-gradient(135deg, #fff 0%, #f0f7ff 100%);
        }

        .priority-card.prevented {
            border-left-color: #f39c12;
            background: linear-gradient(135deg, #fff 0%, #fef9f0 100%);
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .card-icon {
            font-size: 24px;
            margin-right: 12px;
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
        }

        .card-value {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 8px;
            color: #2c3e50;
        }

        .card-subtitle {
            font-size: 14px;
            color: #7f8c8d;
            margin-bottom: 15px;
        }

        .card-action {
            width: 100%;
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .urgent .card-action {
            background: #e74c3c;
            color: white;
        }

        .potential .card-action {
            background: #27ae60;
            color: white;
        }

        .realized .card-action {
            background: #3498db;
            color: white;
        }

        .prevented .card-action {
            background: #f39c12;
            color: white;
        }

        .card-action:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        /* Seção de ações */
        .actions-section {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .actions-list {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }

        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 20px;
        }

        .action-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 10px;
            border-left: 4px solid;
            transition: all 0.3s ease;
        }

        .action-item:hover {
            transform: translateX(5px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .action-item.urgent-action {
            background: #fdf2f2;
            border-left-color: #e74c3c;
        }

        .action-item.warning-action {
            background: #fef9f0;
            border-left-color: #f39c12;
        }

        .action-item.success-action {
            background: #f0f9f4;
            border-left-color: #27ae60;
        }

        .action-info {
            flex: 1;
        }

        .action-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .action-detail {
            font-size: 12px;
            color: #7f8c8d;
        }

        .action-status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            margin-right: 10px;
        }

        .status-urgent {
            background: #e74c3c;
            color: white;
        }

        .status-warning {
            background: #f39c12;
            color: white;
        }

        .status-resolved {
            background: #27ae60;
            color: white;
        }

        .action-button {
            padding: 8px 15px;
            border: none;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-resolve {
            background: #e74c3c;
            color: white;
        }

        .btn-contest {
            background: #95a5a6;
            color: white;
        }

        .btn-resolve:hover, .btn-contest:hover {
            transform: translateY(-2px);
        }

        /* Gráfico de tendência */
        .trend-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            position: relative;
        }

        .trend-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .chart-container {
            position: relative;
            height: 300px;
            margin-bottom: 20px;
        }

        .insight-box {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #2196f3;
            font-size: 14px;
            color: #1565c0;
        }

        .zoom-tip {
            position: absolute;
            top: 20px;
            right: 20px;
            background: #2196f3;
            color: white;
            padding: 10px 15px;
            border-radius: 8px;
            font-size: 12px;
            font-weight: 600;
            z-index: 10;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        /* Seção inferior */
        .bottom-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }

        .patterns-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }

        .pattern-item {
            display: flex;
            align-items: center;
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 10px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-left: 4px solid #ffc107;
        }

        .pattern-icon {
            font-size: 20px;
            margin-right: 15px;
        }

        .pattern-text {
            flex: 1;
        }

        .pattern-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .pattern-detail {
            font-size: 12px;
            color: #6c757d;
        }

        .pattern-action {
            padding: 8px 15px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .pattern-action:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }

        .vehicles-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }

        .vehicle-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 10px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-left: 4px solid #dc3545;
        }

        .vehicle-info {
            flex: 1;
        }

        .vehicle-id {
            font-weight: 700;
            color: #dc3545;
            font-size: 16px;
            margin-bottom: 5px;
        }

        .vehicle-detail {
            font-size: 12px;
            color: #6c757d;
        }

        .vehicle-actions {
            display: flex;
            gap: 10px;
        }

        .btn-generate {
            padding: 8px 15px;
            background: #17a2b8;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-generate:hover {
            background: #138496;
            transform: translateY(-2px);
        }

        /* Responsividade */
        @media (max-width: 1200px) {
            .actions-section {
                grid-template-columns: 1fr;
            }
            .bottom-section {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .main-cards {
                grid-template-columns: 1fr;
            }
            .header {
                flex-direction: column;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div>
                <h1>Dashboard Inteligente: O que Realmente Importa</h1>
                <div class="header-subtitle">CaçaMultas - Revolução UX</div>
            </div>
        </div>

        <!-- Cards Principais -->
        <div class="main-cards">
            <div class="priority-card urgent">
                <div class="card-header">
                    <div class="card-icon">🚨</div>
                    <div class="card-title">Atenção Imediata</div>
                </div>
                <div class="card-value">R$ 1.650</div>
                <div class="card-subtitle">5 multas vencendo esta semana</div>
                <button class="card-action">⚡ Resolver Agora</button>
            </div>

            <div class="priority-card potential">
                <div class="card-header">
                    <div class="card-icon">💰</div>
                    <div class="card-title">Economia Potencial</div>
                </div>
                <div class="card-value">R$ 3.200</div>
                <div class="card-subtitle">25 multas contestáveis</div>
                <button class="card-action">📋 Contestar</button>
            </div>

            <div class="priority-card realized">
                <div class="card-header">
                    <div class="card-icon">📈</div>
                    <div class="card-title">Economia Realizada</div>
                </div>
                <div class="card-value">R$ 1.800</div>
                <div class="card-subtitle">Último mês - 90%</div>
                <button class="card-action">📊 Ver Detalhes</button>
            </div>

            <div class="priority-card prevented">
                <div class="card-header">
                    <div class="card-icon">🛡️</div>
                    <div class="card-title">Multas NIC Evitadas</div>
                </div>
                <div class="card-value">R$ 2.400</div>
                <div class="card-subtitle">12 multas prevenidas</div>
                <button class="card-action">🔍 Ver Motoristas</button>
            </div>
        </div>

        <!-- Seção de Ações e Tendência -->
        <div class="actions-section">
            <!-- Lista de Ações Necessárias -->
            <div class="actions-list">
                <h2 class="section-title">Ações Necessárias</h2>

                <div class="action-item urgent-action">
                    <div class="action-info">
                        <div class="action-title">● GAL9759</div>
                        <div class="action-detail">Velocidade - R$ 130,16</div>
                        <div class="action-detail">61 multa no mesmo local</div>
                    </div>
                    <div class="action-status status-urgent">Vence hoje</div>
                    <div>
                        <button class="action-button btn-resolve">Pagar</button>
                        <button class="action-button btn-contest">Contestar</button>
                    </div>
                </div>

                <div class="action-item warning-action">
                    <div class="action-info">
                        <div class="action-title">● BVT4251</div>
                        <div class="action-detail">Local Proibido - R$ 131,46</div>
                    </div>
                    <div class="action-status status-warning">2 dias</div>
                    <div>
                        <button class="action-button btn-resolve">Resolver</button>
                    </div>
                </div>

                <div class="action-item success-action">
                    <div class="action-info">
                        <div class="action-title">● FXW0843</div>
                        <div class="action-detail">Velocidade - R$ 130,16</div>
                        <div class="action-detail">Pagamento confirmado</div>
                    </div>
                    <div class="action-status status-resolved">Resolvido</div>
                </div>
            </div>

            <!-- Gráfico de Tendência -->
            <div class="trend-section">
                <div class="trend-header">
                    <h2 class="section-title">Tendência de Multas</h2>
                    <div style="font-size: 14px; color: #7f8c8d;">Últimos 6 meses por valor e quantidade</div>
                </div>
                <div class="zoom-tip">
                    Reduza o zoom para ver padrões de multas e identificar tendências!
                </div>
                <div class="chart-container">
                    <canvas id="trendChart"></canvas>
                </div>
                <div class="insight-box">
                    💡 <strong>Sistema identifica padrões automaticamente e sugere ações!</strong>
                </div>
            </div>
        </div>

        <!-- Seção Inferior -->
        <div class="bottom-section">
            <!-- Padrões Detectados -->
            <div class="patterns-section">
                <h2 class="section-title">Padrões Detectados</h2>

                <div class="pattern-item">
                    <div class="pattern-icon">🔄</div>
                    <div class="pattern-text">
                        <div class="pattern-title">Múltiplas multas - Mesmo local</div>
                        <div class="pattern-detail">6 multas na Av. Alcântara Machado - Sempre às 13h</div>
                    </div>
                    <button class="pattern-action">Contestar em Lote</button>
                </div>

                <div class="pattern-item">
                    <div class="pattern-icon">👤</div>
                    <div class="pattern-text">
                        <div class="pattern-title">Motorista com múltiplas infrações</div>
                        <div class="pattern-detail">João Silva - 8 multas de velocidade em 30 dias</div>
                    </div>
                    <button class="pattern-action">Alterar Rota</button>
                </div>

                <div class="pattern-item">
                    <div class="pattern-icon">🚛</div>
                    <div class="pattern-text">
                        <div class="pattern-title">Veículo com padrão suspeito</div>
                        <div class="pattern-detail">GAL9759 - Sempre multado no mesmo horário/local</div>
                    </div>
                    <button class="pattern-action">Programa de Treinamento</button>
                </div>

                <div class="pattern-item">
                    <div class="pattern-icon">⚠️</div>
                    <div class="pattern-text">
                        <div class="pattern-title">Radar com alta incidência</div>
                        <div class="pattern-detail">BR-116 KM 15 - 45% das multas da frota</div>
                    </div>
                    <button class="pattern-action">Ver Detalhes</button>
                </div>
            </div>

            <!-- Top Veículos -->
            <div class="vehicles-section">
                <h2 class="section-title">Top Veículos</h2>

                <div class="vehicle-item">
                    <div class="vehicle-info">
                        <div class="vehicle-id">1 GAL9759</div>
                        <div class="vehicle-detail">13 multas - R$ 1.690</div>
                    </div>
                    <div class="vehicle-actions">
                        <button class="btn-generate">Gerenciar</button>
                    </div>
                </div>

                <div class="vehicle-item">
                    <div class="vehicle-info">
                        <div class="vehicle-id">2 BVT4251</div>
                        <div class="vehicle-detail">8 multas - R$ 1.052</div>
                    </div>
                    <div class="vehicle-actions">
                        <button class="btn-generate">Gerenciar</button>
                    </div>
                </div>

                <div class="vehicle-item">
                    <div class="vehicle-info">
                        <div class="vehicle-id">3 FXW0843</div>
                        <div class="vehicle-detail">5 multas - R$ 650</div>
                    </div>
                    <div class="vehicle-actions">
                        <button class="btn-generate">Gerenciar</button>
                    </div>
                </div>

                <div style="background: #e8f4fd; padding: 15px; border-radius: 10px; margin-top: 20px; font-size: 14px; color: #1565c0;">
                    💡 <strong>Gestor identifica rapidamente quais veículos precisam de atenção!</strong>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Configuração global dos gráficos
        Chart.defaults.font.family = "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif";
        Chart.defaults.font.size = 12;

        // Gráfico de Tendência com dados reais do protótipo
        const trendCtx = document.getElementById('trendChart').getContext('2d');
        new Chart(trendCtx, {
            type: 'line',
            data: {
                labels: ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun'],
                datasets: [
                    {
                        label: 'Valor (R$)',
                        data: [8000, 9500, 7200, 6800, 5900, 4500],
                        borderColor: '#e74c3c',
                        backgroundColor: 'rgba(231, 76, 60, 0.1)',
                        yAxisID: 'y',
                        tension: 0.4,
                        fill: true
                    },
                    {
                        label: 'Quantidade',
                        data: [50, 40, 35, 30, 25, 20],
                        borderColor: '#3498db',
                        backgroundColor: 'rgba(52, 152, 219, 0.1)',
                        yAxisID: 'y1',
                        tension: 0.4,
                        fill: false
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 20
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                if (context.datasetIndex === 0) {
                                    return `Valor: R$ ${context.parsed.y.toLocaleString('pt-BR')}`;
                                } else {
                                    return `Quantidade: ${context.parsed.y} multas`;
                                }
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        display: true,
                        title: {
                            display: true,
                            text: 'Mês'
                        },
                        grid: {
                            display: false
                        }
                    },
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'Valor (R$)',
                            color: '#e74c3c'
                        },
                        ticks: {
                            callback: function(value) {
                                return 'R$ ' + value.toLocaleString('pt-BR');
                            },
                            color: '#e74c3c'
                        },
                        grid: {
                            color: 'rgba(231, 76, 60, 0.1)'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'Quantidade',
                            color: '#3498db'
                        },
                        ticks: {
                            color: '#3498db'
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                    },
                }
            }
        });

        // Animações de entrada
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.priority-card, .action-item, .pattern-item, .vehicle-item');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });

        // Funções de interação
        function resolverMulta(id) {
            alert(`Resolvendo multa ${id}...`);
        }

        function contestarMulta(id) {
            alert(`Contestando multa ${id}...`);
        }

        function gerenciarVeiculo(id) {
            alert(`Gerenciando veículo ${id}...`);
        }

        function executarAcao(acao) {
            alert(`Executando ação: ${acao}`);
        }
    </script>
</body>
</html>
