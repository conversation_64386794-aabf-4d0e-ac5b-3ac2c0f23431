<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard CaçaMultas - Análises Detalhadas</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8fafc;
            color: #334155;
        }

        .header {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            color: white;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 24px;
            font-weight: 600;
        }

        .filters {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .filter-select {
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 14px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .analysis-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .chart-panel {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f1f5f9;
        }

        .chart-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
        }

        .chart-controls {
            display: flex;
            gap: 10px;
        }

        .chart-btn {
            background: #f1f5f9;
            border: none;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .chart-btn.active {
            background: #3b82f6;
            color: white;
        }

        .insights-panel {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }

        .insight-item {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            border-left: 4px solid #3b82f6;
        }

        .insight-item.warning {
            border-left-color: #f59e0b;
            background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
        }

        .insight-item.success {
            border-left-color: #10b981;
            background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
        }

        .insight-item.danger {
            border-left-color: #ef4444;
            background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
        }

        .insight-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: #1e293b;
        }

        .insight-description {
            font-size: 13px;
            color: #64748b;
            line-height: 1.4;
        }

        .cnpj-analysis {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .cnpj-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }

        .cnpj-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .cnpj-title {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
        }

        .cnpj-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-critical {
            background: #fef2f2;
            color: #dc2626;
        }

        .status-warning {
            background: #fffbeb;
            color: #d97706;
        }

        .status-good {
            background: #f0fdf4;
            color: #16a34a;
        }

        .cnpj-metrics {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }

        .cnpj-metric {
            text-align: center;
            padding: 10px;
            background: #f8fafc;
            border-radius: 8px;
        }

        .cnpj-metric-value {
            font-size: 20px;
            font-weight: bold;
            color: #1e293b;
        }

        .cnpj-metric-label {
            font-size: 11px;
            color: #64748b;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .trend-indicator {
            display: inline-flex;
            align-items: center;
            font-size: 12px;
            margin-top: 5px;
        }

        .trend-up {
            color: #dc2626;
        }

        .trend-down {
            color: #16a34a;
        }

        .detailed-table {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            overflow-x: auto;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th {
            background: #f8fafc;
            padding: 12px;
            text-align: left;
            font-weight: 600;
            color: #374151;
            border-bottom: 2px solid #e5e7eb;
        }

        .table td {
            padding: 12px;
            border-bottom: 1px solid #f3f4f6;
        }

        .table tr:hover {
            background: #f9fafb;
        }

        .priority-high {
            background: #fef2f2;
            color: #dc2626;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
        }

        .priority-medium {
            background: #fffbeb;
            color: #d97706;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
        }

        .priority-low {
            background: #f0fdf4;
            color: #16a34a;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
        }

        .action-btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 11px;
            cursor: pointer;
            margin-right: 5px;
        }

        .action-btn.secondary {
            background: #6b7280;
        }

        @media (max-width: 768px) {
            .analysis-grid {
                grid-template-columns: 1fr;
            }
            
            .cnpj-analysis {
                grid-template-columns: 1fr;
            }
            
            .header-content {
                flex-direction: column;
                gap: 15px;
            }
            
            .filters {
                flex-wrap: wrap;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1>📊 Análises Detalhadas - CaçaMultas Embarcador</h1>
            <div class="filters">
                <select class="filter-select">
                    <option>Últimos 6 meses</option>
                    <option>Último ano</option>
                    <option>Período customizado</option>
                </select>
                <select class="filter-select">
                    <option>Todos os CNPJs</option>
                    <option>CNPJ Principal</option>
                    <option>Filiais</option>
                </select>
                <select class="filter-select">
                    <option>Todos os órgãos</option>
                    <option>DER/PR</option>
                    <option>PRF</option>
                    <option>DETRAN</option>
                </select>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Análise Principal -->
        <div class="analysis-grid">
            <div class="chart-panel">
                <div class="chart-header">
                    <div class="chart-title">Evolução Temporal das Multas</div>
                    <div class="chart-controls">
                        <button class="chart-btn active">Valor</button>
                        <button class="chart-btn">Quantidade</button>
                        <button class="chart-btn">Ambos</button>
                    </div>
                </div>
                <div style="position: relative; height: 350px;">
                    <canvas id="evolutionChart"></canvas>
                </div>
            </div>

            <div class="insights-panel">
                <h3 style="margin-bottom: 20px; color: #1e293b;">🔍 Insights Automáticos</h3>
                
                <div class="insight-item success">
                    <div class="insight-title">📉 Redução Significativa</div>
                    <div class="insight-description">
                        Multas reduziram 23% nos últimos 3 meses. Estratégias de prevenção estão funcionando!
                    </div>
                </div>

                <div class="insight-item warning">
                    <div class="insight-title">⚠️ Pico em Maio</div>
                    <div class="insight-description">
                        Aumento de 45% em multas de velocidade. Verificar se houve mudança de rotas ou novos motoristas.
                    </div>
                </div>

                <div class="insight-item danger">
                    <div class="insight-title">🚨 CNPJ Crítico</div>
                    <div class="insight-description">
                        CNPJ 78015690000140 representa 67% das multas. Ação imediata necessária.
                    </div>
                </div>

                <div class="insight-item">
                    <div class="insight-title">💡 Oportunidade</div>
                    <div class="insight-description">
                        R$ 3.200 em multas contestáveis identificadas. Potencial economia de 15%.
                    </div>
                </div>
            </div>
        </div>

        <!-- Análise por CNPJ -->
        <div class="cnpj-analysis">
            <div class="cnpj-card">
                <div class="cnpj-header">
                    <div class="cnpj-title">CNPJ: 78015690000140</div>
                    <div class="cnpj-status status-critical">Crítico</div>
                </div>
                <div class="cnpj-metrics">
                    <div class="cnpj-metric">
                        <div class="cnpj-metric-value">284</div>
                        <div class="cnpj-metric-label">Multas</div>
                        <div class="trend-indicator trend-up">↗️ +15%</div>
                    </div>
                    <div class="cnpj-metric">
                        <div class="cnpj-metric-value">R$ 18.4k</div>
                        <div class="cnpj-metric-label">Valor Total</div>
                        <div class="trend-indicator trend-up">↗️ +22%</div>
                    </div>
                </div>
                <div style="background: #fef2f2; padding: 10px; border-radius: 6px; font-size: 12px; color: #dc2626;">
                    <strong>Ação Requerida:</strong> 67% das multas da empresa. Implementar programa de treinamento urgente.
                </div>
            </div>

            <div class="cnpj-card">
                <div class="cnpj-header">
                    <div class="cnpj-title">CNPJ: 78015690000221</div>
                    <div class="cnpj-status status-good">Bom</div>
                </div>
                <div class="cnpj-metrics">
                    <div class="cnpj-metric">
                        <div class="cnpj-metric-value">45</div>
                        <div class="cnpj-metric-label">Multas</div>
                        <div class="trend-indicator trend-down">↘️ -8%</div>
                    </div>
                    <div class="cnpj-metric">
                        <div class="cnpj-metric-value">R$ 2.8k</div>
                        <div class="cnpj-metric-label">Valor Total</div>
                        <div class="trend-indicator trend-down">↘️ -12%</div>
                    </div>
                </div>
                <div style="background: #f0fdf4; padding: 10px; border-radius: 6px; font-size: 12px; color: #16a34a;">
                    <strong>Performance Excelente:</strong> Redução consistente. Usar como benchmark para outras unidades.
                </div>
            </div>
        </div>

        <!-- Tabela Detalhada -->
        <div class="detailed-table">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h3 style="color: #1e293b;">📋 Multas Prioritárias para Ação</h3>
                <div>
                    <button class="action-btn">📊 Exportar</button>
                    <button class="action-btn secondary">⚙️ Filtros</button>
                </div>
            </div>
            
            <table class="table">
                <thead>
                    <tr>
                        <th>Prioridade</th>
                        <th>Veículo</th>
                        <th>Infração</th>
                        <th>Órgão</th>
                        <th>Valor</th>
                        <th>Vencimento</th>
                        <th>Economia Potencial</th>
                        <th>Ações</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><span class="priority-high">ALTA</span></td>
                        <td><strong>GAL9759</strong></td>
                        <td>Excesso de Velocidade</td>
                        <td>DER/PR</td>
                        <td><strong>R$ 130,16</strong></td>
                        <td style="color: #dc2626;"><strong>Hoje</strong></td>
                        <td style="color: #16a34a;"><strong>R$ 65,08</strong></td>
                        <td>
                            <button class="action-btn">Pagar</button>
                            <button class="action-btn secondary">Contestar</button>
                        </td>
                    </tr>
                    <tr>
                        <td><span class="priority-high">ALTA</span></td>
                        <td><strong>BVT4251</strong></td>
                        <td>Local Proibido</td>
                        <td>Prefeitura SP</td>
                        <td><strong>R$ 131,46</strong></td>
                        <td style="color: #d97706;"><strong>2 dias</strong></td>
                        <td style="color: #16a34a;"><strong>R$ 65,73</strong></td>
                        <td>
                            <button class="action-btn">Resolver</button>
                        </td>
                    </tr>
                    <tr>
                        <td><span class="priority-medium">MÉDIA</span></td>
                        <td><strong>FXW0B43</strong></td>
                        <td>Excesso de Peso</td>
                        <td>PRF</td>
                        <td><strong>R$ 195,23</strong></td>
                        <td>7 dias</td>
                        <td style="color: #16a34a;"><strong>R$ 97,62</strong></td>
                        <td>
                            <button class="action-btn">Analisar</button>
                            <button class="action-btn secondary">Detalhes</button>
                        </td>
                    </tr>
                    <tr>
                        <td><span class="priority-medium">MÉDIA</span></td>
                        <td><strong>ACD2283</strong></td>
                        <td>Transitar c/ Excesso</td>
                        <td>DER/PR</td>
                        <td><strong>R$ 191,53</strong></td>
                        <td>12 dias</td>
                        <td style="color: #16a34a;"><strong>R$ 95,77</strong></td>
                        <td>
                            <button class="action-btn">Contestar</button>
                        </td>
                    </tr>
                    <tr>
                        <td><span class="priority-low">BAIXA</span></td>
                        <td><strong>AVP8001</strong></td>
                        <td>Velocidade</td>
                        <td>DETRAN/PR</td>
                        <td><strong>R$ 130,16</strong></td>
                        <td>25 dias</td>
                        <td style="color: #16a34a;"><strong>R$ 65,08</strong></td>
                        <td>
                            <button class="action-btn secondary">Monitorar</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        // Gráfico de Evolução Temporal
        const evolutionCtx = document.getElementById('evolutionChart').getContext('2d');
        new Chart(evolutionCtx, {
            type: 'line',
            data: {
                labels: ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun'],
                datasets: [{
                    label: 'Valor Total (R$)',
                    data: [12500, 11200, 9800, 8900, 12800, 9200],
                    borderColor: '#ef4444',
                    backgroundColor: 'rgba(239, 68, 68, 0.1)',
                    tension: 0.4,
                    fill: true,
                    pointBackgroundColor: '#ef4444',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 6
                }, {
                    label: 'Quantidade',
                    data: [85, 78, 65, 58, 84, 62],
                    borderColor: '#3b82f6',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    tension: 0.4,
                    yAxisID: 'y1',
                    pointBackgroundColor: '#3b82f6',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 6
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'Valor (R$)'
                        },
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'Quantidade'
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 20
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0,0,0,0.8)',
                        titleColor: 'white',
                        bodyColor: 'white',
                        borderColor: 'rgba(255,255,255,0.2)',
                        borderWidth: 1
                    }
                }
            }
        });

        // Interatividade dos botões
        document.querySelectorAll('.chart-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.chart-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
            });
        });
    </script>
</body>
</html>
