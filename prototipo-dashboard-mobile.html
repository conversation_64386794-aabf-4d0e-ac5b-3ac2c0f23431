<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CaçaMultas Mobile - Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f8fafc;
            color: #1e293b;
            line-height: 1.5;
        }

        .mobile-header {
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            color: white;
            padding: 15px 20px;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .app-title {
            font-size: 18px;
            font-weight: 600;
        }

        .notification-badge {
            background: #ef4444;
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }

        .quick-stats {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
        }

        .quick-stat {
            text-align: center;
        }

        .quick-stat-value {
            font-weight: bold;
            font-size: 14px;
        }

        .container {
            padding: 15px;
        }

        .alert-banner {
            background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
            border: 1px solid #fecaca;
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .alert-icon {
            font-size: 24px;
            flex-shrink: 0;
        }

        .alert-content h3 {
            color: #dc2626;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .alert-content p {
            color: #7f1d1d;
            font-size: 12px;
        }

        .alert-action {
            background: #dc2626;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 11px;
            font-weight: 600;
            margin-top: 8px;
            width: 100%;
        }

        .metrics-mobile {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 20px;
        }

        .metric-card-mobile {
            background: white;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            text-align: center;
            border-top: 3px solid;
        }

        .metric-card-mobile.urgent {
            border-top-color: #ef4444;
        }

        .metric-card-mobile.economy {
            border-top-color: #10b981;
        }

        .metric-card-mobile.realized {
            border-top-color: #3b82f6;
        }

        .metric-card-mobile.prevented {
            border-top-color: #f59e0b;
        }

        .metric-icon-mobile {
            font-size: 20px;
            margin-bottom: 8px;
        }

        .metric-value-mobile {
            font-size: 18px;
            font-weight: bold;
            color: #1e293b;
            margin-bottom: 4px;
        }

        .metric-label-mobile {
            font-size: 11px;
            color: #64748b;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
        }

        .see-all {
            color: #3b82f6;
            font-size: 12px;
            text-decoration: none;
            font-weight: 500;
        }

        .actions-mobile {
            background: white;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            margin-bottom: 20px;
        }

        .action-item-mobile {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f1f5f9;
        }

        .action-item-mobile:last-child {
            border-bottom: none;
        }

        .action-priority {
            width: 8px;
            height: 40px;
            border-radius: 4px;
            margin-right: 12px;
            flex-shrink: 0;
        }

        .priority-urgent {
            background: #ef4444;
        }

        .priority-warning {
            background: #f59e0b;
        }

        .priority-normal {
            background: #10b981;
        }

        .action-details {
            flex: 1;
        }

        .action-vehicle {
            font-weight: 600;
            color: #1e293b;
            font-size: 14px;
        }

        .action-info {
            font-size: 12px;
            color: #64748b;
            margin-top: 2px;
        }

        .action-value {
            font-weight: 600;
            color: #ef4444;
        }

        .action-due {
            text-align: right;
            font-size: 11px;
        }

        .due-urgent {
            color: #ef4444;
            font-weight: 600;
        }

        .due-warning {
            color: #f59e0b;
            font-weight: 600;
        }

        .chart-mobile {
            background: white;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            margin-bottom: 20px;
        }

        .chart-tabs {
            display: flex;
            background: #f1f5f9;
            border-radius: 8px;
            padding: 4px;
            margin-bottom: 15px;
        }

        .chart-tab {
            flex: 1;
            background: transparent;
            border: none;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            color: #64748b;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .chart-tab.active {
            background: white;
            color: #1e293b;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .simple-chart {
            height: 120px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 8px;
            display: flex;
            align-items: end;
            padding: 10px;
            gap: 8px;
        }

        .chart-bar {
            background: #3b82f6;
            border-radius: 2px 2px 0 0;
            min-width: 20px;
            position: relative;
        }

        .chart-bar:nth-child(1) { height: 60%; background: #ef4444; }
        .chart-bar:nth-child(2) { height: 45%; background: #f59e0b; }
        .chart-bar:nth-child(3) { height: 80%; background: #10b981; }
        .chart-bar:nth-child(4) { height: 35%; background: #3b82f6; }
        .chart-bar:nth-child(5) { height: 55%; background: #8b5cf6; }
        .chart-bar:nth-child(6) { height: 25%; background: #06b6d4; }

        .insights-mobile {
            background: white;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
        }

        .insight-mobile {
            background: #f8fafc;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 12px;
            border-left: 3px solid #3b82f6;
        }

        .insight-mobile:last-child {
            margin-bottom: 0;
        }

        .insight-mobile.warning {
            border-left-color: #f59e0b;
            background: #fffbeb;
        }

        .insight-mobile.success {
            border-left-color: #10b981;
            background: #f0fdf4;
        }

        .insight-title-mobile {
            font-size: 13px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 4px;
        }

        .insight-text-mobile {
            font-size: 12px;
            color: #64748b;
            line-height: 1.4;
        }

        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #e2e8f0;
            display: flex;
            padding: 8px 0;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
        }

        .nav-item {
            flex: 1;
            text-align: center;
            padding: 8px;
            color: #64748b;
            text-decoration: none;
            font-size: 11px;
        }

        .nav-item.active {
            color: #3b82f6;
        }

        .nav-icon {
            font-size: 18px;
            margin-bottom: 2px;
            display: block;
        }

        .fab {
            position: fixed;
            bottom: 80px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, #ef4444, #dc2626);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
            border: none;
            cursor: pointer;
        }

        /* Adicionar espaço para a navegação inferior */
        body {
            padding-bottom: 70px;
        }
    </style>
</head>
<body>
    <div class="mobile-header">
        <div class="header-top">
            <div class="app-title">🚛 CaçaMultas</div>
            <div class="notification-badge">5</div>
        </div>
        <div class="quick-stats">
            <div class="quick-stat">
                <div class="quick-stat-value">418</div>
                <div>Total</div>
            </div>
            <div class="quick-stat">
                <div class="quick-stat-value">5</div>
                <div>Urgentes</div>
            </div>
            <div class="quick-stat">
                <div class="quick-stat-value">R$ 27k</div>
                <div>Valor</div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Alerta Urgente -->
        <div class="alert-banner">
            <div class="alert-icon">🚨</div>
            <div class="alert-content">
                <h3>5 multas vencendo hoje!</h3>
                <p>R$ 1.650 em risco. Ação imediata necessária.</p>
                <button class="alert-action">Resolver Agora</button>
            </div>
        </div>

        <!-- Métricas Principais -->
        <div class="metrics-mobile">
            <div class="metric-card-mobile economy">
                <div class="metric-icon-mobile">💰</div>
                <div class="metric-value-mobile">R$ 3.200</div>
                <div class="metric-label-mobile">Economia Potencial</div>
            </div>
            <div class="metric-card-mobile realized">
                <div class="metric-icon-mobile">📈</div>
                <div class="metric-value-mobile">R$ 1.800</div>
                <div class="metric-label-mobile">Economia Realizada</div>
            </div>
            <div class="metric-card-mobile urgent">
                <div class="metric-icon-mobile">⚠️</div>
                <div class="metric-value-mobile">5</div>
                <div class="metric-label-mobile">Multas Urgentes</div>
            </div>
            <div class="metric-card-mobile prevented">
                <div class="metric-icon-mobile">🛡️</div>
                <div class="metric-value-mobile">12</div>
                <div class="metric-label-mobile">NIC Evitadas</div>
            </div>
        </div>

        <!-- Ações Necessárias -->
        <div class="actions-mobile">
            <div class="section-header">
                <div class="section-title">⚡ Ações Necessárias</div>
                <a href="#" class="see-all">Ver todas</a>
            </div>

            <div class="action-item-mobile">
                <div class="action-priority priority-urgent"></div>
                <div class="action-details">
                    <div class="action-vehicle">GAL9759</div>
                    <div class="action-info">Velocidade • <span class="action-value">R$ 130,16</span></div>
                </div>
                <div class="action-due">
                    <div class="due-urgent">Hoje</div>
                    <div>Vence</div>
                </div>
            </div>

            <div class="action-item-mobile">
                <div class="action-priority priority-warning"></div>
                <div class="action-details">
                    <div class="action-vehicle">BVT4251</div>
                    <div class="action-info">Local Proibido • <span class="action-value">R$ 131,46</span></div>
                </div>
                <div class="action-due">
                    <div class="due-warning">2 dias</div>
                    <div>Restam</div>
                </div>
            </div>

            <div class="action-item-mobile">
                <div class="action-priority priority-normal"></div>
                <div class="action-details">
                    <div class="action-vehicle">FXW0B43</div>
                    <div class="action-info">Resolvido • <span style="color: #10b981;">Pago</span></div>
                </div>
                <div class="action-due">
                    <div style="color: #10b981;">✓</div>
                    <div>OK</div>
                </div>
            </div>
        </div>

        <!-- Gráfico Simples -->
        <div class="chart-mobile">
            <div class="section-header">
                <div class="section-title">📊 Tendências</div>
            </div>
            
            <div class="chart-tabs">
                <button class="chart-tab active">6 meses</button>
                <button class="chart-tab">Por órgão</button>
                <button class="chart-tab">Por CNPJ</button>
            </div>

            <div class="simple-chart">
                <div class="chart-bar"></div>
                <div class="chart-bar"></div>
                <div class="chart-bar"></div>
                <div class="chart-bar"></div>
                <div class="chart-bar"></div>
                <div class="chart-bar"></div>
            </div>

            <div style="display: flex; justify-content: space-between; margin-top: 8px; font-size: 10px; color: #64748b;">
                <span>Jan</span>
                <span>Fev</span>
                <span>Mar</span>
                <span>Abr</span>
                <span>Mai</span>
                <span>Jun</span>
            </div>
        </div>

        <!-- Insights -->
        <div class="insights-mobile">
            <div class="section-header">
                <div class="section-title">🔍 Insights</div>
            </div>

            <div class="insight-mobile success">
                <div class="insight-title-mobile">📉 Redução de 23%</div>
                <div class="insight-text-mobile">Multas reduziram significativamente nos últimos 3 meses.</div>
            </div>

            <div class="insight-mobile warning">
                <div class="insight-title-mobile">⚠️ CNPJ Crítico</div>
                <div class="insight-text-mobile">78015690000140 representa 67% das multas da empresa.</div>
            </div>

            <div class="insight-mobile">
                <div class="insight-title-mobile">💡 Oportunidade</div>
                <div class="insight-text-mobile">R$ 3.200 em multas contestáveis identificadas.</div>
            </div>
        </div>
    </div>

    <!-- Botão de Ação Flutuante -->
    <button class="fab">+</button>

    <!-- Navegação Inferior -->
    <div class="bottom-nav">
        <a href="#" class="nav-item active">
            <span class="nav-icon">🏠</span>
            Dashboard
        </a>
        <a href="#" class="nav-item">
            <span class="nav-icon">📋</span>
            Multas
        </a>
        <a href="#" class="nav-item">
            <span class="nav-icon">📊</span>
            Análises
        </a>
        <a href="#" class="nav-item">
            <span class="nav-icon">⚙️</span>
            Config
        </a>
    </div>

    <script>
        // Interatividade das abas do gráfico
        document.querySelectorAll('.chart-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelectorAll('.chart-tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // Simulação de atualização em tempo real
        setInterval(() => {
            const badge = document.querySelector('.notification-badge');
            const currentValue = parseInt(badge.textContent);
            if (Math.random() > 0.8) {
                badge.textContent = currentValue + 1;
                badge.style.animation = 'pulse 0.5s ease-in-out';
                setTimeout(() => {
                    badge.style.animation = '';
                }, 500);
            }
        }, 10000);
    </script>

    <style>
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }
    </style>
</body>
</html>
