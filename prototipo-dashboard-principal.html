<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard CaçaMultas - EMPRESA TESTE PR</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f7fa;
            color: #333;
        }

        .header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 28px;
            margin-bottom: 5px;
        }

        .header p {
            opacity: 0.9;
            font-size: 16px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .metric-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            border-left: 5px solid;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            position: relative;
            overflow: hidden;
        }

        .metric-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.12);
        }

        .metric-card.urgent {
            border-left-color: #e74c3c;
            background: linear-gradient(135deg, #fff 0%, #fdf2f2 100%);
        }

        .metric-card.economy {
            border-left-color: #27ae60;
            background: linear-gradient(135deg, #fff 0%, #f2fdf5 100%);
        }

        .metric-card.realized {
            border-left-color: #3498db;
            background: linear-gradient(135deg, #fff 0%, #f2f8fd 100%);
        }

        .metric-card.prevented {
            border-left-color: #f39c12;
            background: linear-gradient(135deg, #fff 0%, #fdf8f2 100%);
        }

        .metric-icon {
            position: absolute;
            top: 20px;
            right: 20px;
            font-size: 24px;
            opacity: 0.3;
        }

        .metric-value {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 8px;
            color: #2c3e50;
        }

        .metric-label {
            font-size: 14px;
            color: #7f8c8d;
            margin-bottom: 15px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .metric-detail {
            font-size: 13px;
            color: #95a5a6;
        }

        .metric-button {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
            margin-top: 10px;
            transition: all 0.2s ease;
        }

        .metric-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
        }

        .metric-button.urgent-btn {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }

        .metric-button.economy-btn {
            background: linear-gradient(135deg, #27ae60, #229954);
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .chart-container {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }

        .chart-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #2c3e50;
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 10px;
        }

        .actions-section {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }

        .action-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            border-left: 4px solid;
            margin-bottom: 12px;
            border-radius: 0 8px 8px 0;
            transition: all 0.2s ease;
        }

        .action-item:hover {
            transform: translateX(5px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .action-item.urgent {
            border-left-color: #e74c3c;
            background: #fdf2f2;
        }

        .action-item.warning {
            border-left-color: #f39c12;
            background: #fdf8f2;
        }

        .action-item.success {
            border-left-color: #27ae60;
            background: #f2fdf5;
        }

        .action-info h4 {
            margin-bottom: 5px;
            color: #2c3e50;
        }

        .action-info p {
            font-size: 13px;
            color: #7f8c8d;
        }

        .action-button {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 600;
            transition: all 0.2s ease;
        }

        .btn-resolve {
            background: #e74c3c;
            color: white;
        }

        .btn-contest {
            background: #3498db;
            color: white;
        }

        .btn-details {
            background: #95a5a6;
            color: white;
        }

        .patterns-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .pattern-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }

        .pattern-card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 16px;
        }

        .vehicle-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #ecf0f1;
        }

        .vehicle-item:last-child {
            border-bottom: none;
        }

        .vehicle-info {
            display: flex;
            align-items: center;
        }

        .vehicle-rank {
            background: #3498db;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            margin-right: 10px;
        }

        .vehicle-details h4 {
            margin-bottom: 2px;
            color: #2c3e50;
        }

        .vehicle-details p {
            font-size: 12px;
            color: #7f8c8d;
        }

        .vehicle-stats {
            text-align: right;
        }

        .vehicle-stats .count {
            font-weight: bold;
            color: #e74c3c;
        }

        .vehicle-stats .value {
            font-size: 12px;
            color: #7f8c8d;
        }

        /* Análises Detalhadas */
        .detailed-analysis {
            margin: 40px 0;
            padding: 30px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .filters-section {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            flex-wrap: wrap;
            align-items: end;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .filter-group label {
            font-weight: 600;
            color: #2c3e50;
            font-size: 12px;
        }

        .filter-group select,
        .filter-group input[type="date"] {
            padding: 8px 12px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 13px;
            min-width: 180px;
            transition: border-color 0.3s ease;
        }

        .filter-group select:focus,
        .filter-group input[type="date"]:focus {
            outline: none;
            border-color: #3498db;
        }

        .filter-button {
            padding: 10px 20px;
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            height: fit-content;
        }

        .filter-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        }

        .analysis-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
        }

        .analysis-card {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }

        .analysis-card:hover {
            transform: translateY(-5px);
        }

        .analysis-card h3 {
            margin-bottom: 20px;
            color: #2c3e50;
            font-size: 16px;
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 10px;
        }

        .insight-box {
            margin-top: 15px;
            padding: 12px;
            background: linear-gradient(135deg, #e8f4fd, #d6eaf8);
            border-radius: 8px;
            border-left: 4px solid #3498db;
            font-size: 12px;
            color: #2c3e50;
        }

        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .patterns-section {
                grid-template-columns: 1fr;
            }
            
            .metrics-grid {
                grid-template-columns: 1fr;
            }

            .filters-section {
                flex-direction: column;
                gap: 15px;
            }

            .filter-group select,
            .filter-group input[type="date"] {
                min-width: 100%;
            }

            .analysis-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚛 Dashboard CaçaMultas - EMPRESA TESTE PR</h1>
    </div>

    <div class="container">
        <!-- Cards de Métricas Principais -->
        <div class="metrics-grid">
            <div class="metric-card urgent">
                <div class="metric-icon">⚠️</div>
                <div class="metric-label">Multas Vencidas</div>
                <div class="metric-value">R$ 27.645,96</div>
                <div class="metric-detail">417 multas vencidas</div>
                <div class="metric-detail">12 vencem esta semana</div>
                <button class="metric-button urgent-btn">⚡ Resolver Agora</button>
            </div>

            <div class="metric-card economy">
                <div class="metric-icon">💰</div>
                <div class="metric-label">Valor Médio</div>
                <div class="metric-value">R$ 297</div>
                <div class="metric-detail">Por multa (418 total)</div>
                <button class="metric-button economy-btn">💡 Analisar</button>
            </div>

            <div class="metric-card realized">
                <div class="metric-icon">📈</div>
                <div class="metric-label">Multa Paga</div>
                <div class="metric-value">1</div>
                <div class="metric-detail">Exemplo de sucesso</div>
                <button class="metric-button">📊 Ver Detalhes</button>
            </div>

            <div class="metric-card prevented">
                <div class="metric-icon">🏢</div>
                <div class="metric-label">CNPJ Críticos</div>
                <div class="metric-value">3</div>
                <div class="metric-detail">Precisam atenção urgente</div>
                <button class="metric-button">👁️ Ver CNPJs</button>
            </div>
        </div>

        <!-- Gráficos Principais -->
        <div class="dashboard-grid">
            <div class="chart-container">
                <div class="chart-title">Tendência de Multas</div>
                <div style="position: relative; height: 300px;">
                    <canvas id="trendChart"></canvas>
                </div>
                <div style="background: #e8f4fd; padding: 10px; border-radius: 6px; margin-top: 10px; font-size: 12px;">
                    💡 <strong>Insight:</strong> Redução de 15% nas multas nos últimos 3 meses. Continue o bom trabalho!
                </div>
            </div>

            <div class="chart-container">
                <div class="chart-title">Multas por Órgão Autuador</div>
                <div style="position: relative; height: 300px;">
                    <canvas id="orgaoChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Análises Detalhadas -->
        <div class="detailed-analysis">
            <h2 style="margin-bottom: 25px; color: #2c3e50; text-align: center;">📊 Análises Detalhadas</h2>

            <!-- Filtros -->
            <div class="filters-section">
                <div class="filter-group">
                    <label>Data Inicial:</label>
                    <input type="date" id="dataInicialFilter" value="2009-01-01">
                </div>
                <div class="filter-group">
                    <label>Data Final:</label>
                    <input type="date" id="dataFinalFilter" value="2024-12-31">
                </div>
                <div class="filter-group">
                    <label>Órgão Autuador:</label>
                    <select id="orgaoFilter">
                        <option value="todos">Todos</option>
                        <option value="DER/PR">DER/PR (412 multas)</option>
                        <option value="DNIT">DNIT (5 multas)</option>
                        <option value="DER/SP">DER/SP (1 multa)</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>CNPJ:</label>
                    <select id="cnpjFilter">
                        <option value="todos">Todos</option>
                        <option value="78015690000140">78.015.690/0001-40 (418 multas)</option>
                    </select>
                </div>
                <button class="filter-button">🔍 Aplicar Filtros</button>
            </div>

            <!-- Gráficos de Análise -->
            <div class="analysis-grid">
                <div class="analysis-card">
                    <h3>📈 Evolução Temporal</h3>
                    <div style="position: relative; height: 250px;">
                        <canvas id="timelineChart"></canvas>
                    </div>
                    <div class="insight-box">
                        <strong>Insight:</strong> Pico em 2011 com 170 multas. Redução drástica após 2013.
                    </div>
                </div>

                <div class="analysis-card">
                    <h3>🏢 Top CNPJs - Veículos</h3>
                    <div style="position: relative; height: 250px;">
                        <canvas id="vehiclesChart"></canvas>
                    </div>
                    <div class="insight-box">
                        <strong>Insight:</strong> CNPJ 78.015.690/0001-40 concentra 418 multas (100% do total).
                    </div>
                </div>

                <div class="analysis-card">
                    <h3>📋 Motivos das Multas</h3>
                    <div style="position: relative; height: 250px;">
                        <canvas id="motivosChart"></canvas>
                    </div>
                    <div class="insight-box">
                        <strong>Insight:</strong> 98% das multas são por excesso de peso.
                    </div>
                </div>

                <div class="analysis-card">
                    <h3>📊 Status das Multas</h3>
                    <div style="position: relative; height: 250px;">
                        <canvas id="statusChart"></canvas>
                    </div>
                    <div class="insight-box">
                        <strong>Crítico:</strong> 99,8% das multas estão vencidas (417 de 418).
                    </div>
                </div>
            </div>
        </div>

        <!-- Ações Necessárias -->
        <div class="actions-section">
            <h2 style="margin-bottom: 20px; color: #2c3e50;">Ações Necessárias por CNPJ</h2>

            <div class="action-item urgent">
                <div class="action-info">
                    <h4>CNPJ: 78.015.690/0001-40</h4>
                    <p>EMPRESA TESTE PR - 417 multas vencidas<br>📍 R$ 27.645,96 total em atraso</p>
                </div>
                <div>
                    <span style="color: #e74c3c; font-weight: bold; margin-right: 10px;">Crítico!</span>
                    <button class="action-button btn-resolve">Analisar</button>
                    <button class="action-button btn-contest">Negociar</button>
                </div>
            </div>

            <div class="action-item warning">
                <div class="action-info">
                    <h4>Veículos Críticos do CNPJ</h4>
                    <p>AVF0141, AIU8401, AKY0874<br>35 multas concentradas (8,4% do total)</p>
                </div>
                <div>
                    <span style="color: #f39c12; font-weight: bold; margin-right: 10px;">Alto risco</span>
                    <button class="action-button btn-resolve">Gerenciar</button>
                </div>
            </div>

            <div class="action-item success">
                <div class="action-info">
                    <h4>Única Multa Paga</h4>
                    <p>1 multa regularizada do CNPJ<br>Exemplo de sucesso</p>
                </div>
                <div>
                    <span style="color: #27ae60; font-weight: bold;">Resolvido</span>
                </div>
            </div>
        </div>

        <!-- Padrões Detectados e Top Veículos -->
        <div class="patterns-section">
            <div class="pattern-card">
                <h3>🔍 Padrões Detectados</h3>
                
                <div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin-bottom: 15px; border-left: 4px solid #ffc107;">
                    <h4 style="color: #856404; margin-bottom: 5px;">⚠️ Concentração em Excesso de Peso</h4>
                    <p style="color: #856404; font-size: 13px;">411 de 418 multas (98%) - DER/PR dominante</p>
                    <button style="background: #007bff; color: white; border: none; padding: 5px 10px; border-radius: 4px; font-size: 11px; margin-top: 8px;">Negociar Lote</button>
                    <button style="background: #6c757d; color: white; border: none; padding: 5px 10px; border-radius: 4px; font-size: 11px; margin-top: 8px; margin-left: 5px;">Revisar Frota</button>
                </div>

                <div style="background: #d1ecf1; padding: 15px; border-radius: 8px; border-left: 4px solid #17a2b8;">
                    <h4 style="color: #0c5460; margin-bottom: 5px;">📈 Pico Histórico em 2011</h4>
                    <p style="color: #0c5460; font-size: 13px;">170 multas em 2011 - Redução significativa após 2013</p>
                    <button style="background: #17a2b8; color: white; border: none; padding: 5px 10px; border-radius: 4px; font-size: 11px; margin-top: 8px;">Analisar Tendência</button>
                    <button style="background: #6c757d; color: white; border: none; padding: 5px 10px; border-radius: 4px; font-size: 11px; margin-top: 8px; margin-left: 5px;">Ver Histórico</button>
                </div>
            </div>

            <div class="pattern-card">
                <h3>🚛 Top Veículos</h3>
                
                <div class="vehicle-item">
                    <div class="vehicle-info">
                        <div class="vehicle-rank">1</div>
                        <div class="vehicle-details">
                            <h4>AVF0141</h4>
                            <p>12 multas - R$ 1.796,51</p>
                        </div>
                    </div>
                    <div class="vehicle-stats">
                        <button style="background: #007bff; color: white; border: none; padding: 4px 8px; border-radius: 4px; font-size: 11px;">Gerenciar</button>
                    </div>
                </div>

                <div class="vehicle-item">
                    <div class="vehicle-info">
                        <div class="vehicle-rank" style="background: #6c757d;">2</div>
                        <div class="vehicle-details">
                            <h4>AIU8401</h4>
                            <p>12 multas - R$ 1.921,73</p>
                        </div>
                    </div>
                    <div class="vehicle-stats">
                        <div class="count">Maior valor total</div>
                        <button style="background: #007bff; color: white; border: none; padding: 4px 8px; border-radius: 4px; font-size: 11px; margin-top: 4px;">Gerenciar</button>
                    </div>
                </div>

                <div class="vehicle-item">
                    <div class="vehicle-info">
                        <div class="vehicle-rank" style="background: #dc3545;">3</div>
                        <div class="vehicle-details">
                            <h4>AKY0874</h4>
                            <p>11 multas - R$ 1.637,97</p>
                        </div>
                    </div>
                    <div class="vehicle-stats">
                        <button style="background: #007bff; color: white; border: none; padding: 4px 8px; border-radius: 4px; font-size: 11px;">Gerenciar</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Gráfico de Tendências
        const trendCtx = document.getElementById('trendChart').getContext('2d');
        new Chart(trendCtx, {
            type: 'line',
            data: {
                labels: ['2009', '2010', '2011', '2012', '2013', '2014', '2015-2024'],
                datasets: [{
                    label: 'Quantidade de Multas',
                    data: [15, 79, 170, 107, 25, 8, 14],
                    borderColor: '#e74c3c',
                    backgroundColor: 'rgba(231, 76, 60, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                    }
                }
            }
        });

        // Gráfico de Órgãos
        const orgaoCtx = document.getElementById('orgaoChart').getContext('2d');
        new Chart(orgaoCtx, {
            type: 'doughnut',
            data: {
                labels: ['DER/PR', 'DNIT', 'DER/SP'],
                datasets: [{
                    data: [412, 5, 1],
                    backgroundColor: [
                        '#e74c3c',
                        '#3498db',
                        '#f39c12'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                    }
                }
            }
        });

        // Gráfico Timeline Detalhado
        const timelineCtx = document.getElementById('timelineChart').getContext('2d');
        new Chart(timelineCtx, {
            type: 'bar',
            data: {
                labels: ['2009', '2010', '2011', '2012', '2013', '2014', '2015', '2016', '2017', '2018', '2019', '2020', '2021', '2022', '2023', '2024'],
                datasets: [{
                    label: 'Multas por Ano',
                    data: [15, 79, 170, 107, 25, 8, 3, 2, 1, 1, 1, 1, 1, 1, 2, 1],
                    backgroundColor: [
                        '#3498db', '#3498db', '#e74c3c', '#f39c12', '#27ae60', '#27ae60',
                        '#95a5a6', '#95a5a6', '#95a5a6', '#95a5a6', '#95a5a6', '#95a5a6',
                        '#95a5a6', '#95a5a6', '#3498db', '#3498db'
                    ],
                    borderColor: '#2c3e50',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { display: false }
                },
                scales: {
                    y: { beginAtZero: true }
                }
            }
        });

        // Gráfico Top CNPJs - Veículos
        const vehiclesCtx = document.getElementById('vehiclesChart').getContext('2d');
        new Chart(vehiclesCtx, {
            type: 'bar',
            data: {
                labels: ['78.015.690/0001-40\nAVF0141', '78.015.690/0001-40\nAIU8401', '78.015.690/0001-40\nAKY0874', '78.015.690/0001-40\nAKY3659', '78.015.690/0001-40\nAEQ4456'],
                datasets: [{
                    label: 'Número de Multas por CNPJ-Veículo',
                    data: [12, 12, 11, 10, 9],
                    backgroundColor: [
                        '#e74c3c', '#e74c3c', '#f39c12', '#f39c12', '#3498db'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                indexAxis: 'y',
                plugins: {
                    legend: { display: false }
                },
                scales: {
                    x: { beginAtZero: true }
                }
            }
        });

        // Gráfico Motivos das Multas
        const motivosCtx = document.getElementById('motivosChart').getContext('2d');
        new Chart(motivosCtx, {
            type: 'doughnut',
            data: {
                labels: ['Excesso de Peso', 'Outras Infrações'],
                datasets: [{
                    data: [411, 7],
                    backgroundColor: ['#e74c3c', '#95a5a6'],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // Gráfico Status das Multas
        const statusCtx = document.getElementById('statusChart').getContext('2d');
        new Chart(statusCtx, {
            type: 'doughnut',
            data: {
                labels: ['Vencidas', 'Pagas'],
                datasets: [{
                    data: [417, 1],
                    backgroundColor: ['#e74c3c', '#27ae60'],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // Funcionalidade dos Filtros
        document.querySelector('.filter-button').addEventListener('click', function() {
            const dataInicial = document.getElementById('dataInicialFilter').value;
            const dataFinal = document.getElementById('dataFinalFilter').value;
            const orgao = document.getElementById('orgaoFilter').value;
            const cnpj = document.getElementById('cnpjFilter').value;

            // Simular aplicação de filtros
            alert(`Filtros aplicados:\nData Inicial: ${dataInicial}\nData Final: ${dataFinal}\nÓrgão Autuador: ${orgao}\nCNPJ: ${cnpj}`);
        });
    </script>
</body>
</html>
