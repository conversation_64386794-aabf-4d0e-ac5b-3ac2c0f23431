<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard CaçaMultas Embarcador - Protótipo</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f7fa;
            color: #333;
        }

        .header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 28px;
            margin-bottom: 5px;
        }

        .header p {
            opacity: 0.9;
            font-size: 16px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .metric-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            border-left: 5px solid;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            position: relative;
            overflow: hidden;
        }

        .metric-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.12);
        }

        .metric-card.urgent {
            border-left-color: #e74c3c;
            background: linear-gradient(135deg, #fff 0%, #fdf2f2 100%);
        }

        .metric-card.economy {
            border-left-color: #27ae60;
            background: linear-gradient(135deg, #fff 0%, #f2fdf5 100%);
        }

        .metric-card.realized {
            border-left-color: #3498db;
            background: linear-gradient(135deg, #fff 0%, #f2f8fd 100%);
        }

        .metric-card.prevented {
            border-left-color: #f39c12;
            background: linear-gradient(135deg, #fff 0%, #fdf8f2 100%);
        }

        .metric-icon {
            position: absolute;
            top: 20px;
            right: 20px;
            font-size: 24px;
            opacity: 0.3;
        }

        .metric-value {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 8px;
            color: #2c3e50;
        }

        .metric-label {
            font-size: 14px;
            color: #7f8c8d;
            margin-bottom: 15px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .metric-detail {
            font-size: 13px;
            color: #95a5a6;
        }

        .metric-button {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
            margin-top: 10px;
            transition: all 0.2s ease;
        }

        .metric-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
        }

        .metric-button.urgent-btn {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }

        .metric-button.economy-btn {
            background: linear-gradient(135deg, #27ae60, #229954);
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .chart-container {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }

        .chart-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #2c3e50;
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 10px;
        }

        .actions-section {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }

        .action-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            border-left: 4px solid;
            margin-bottom: 12px;
            border-radius: 0 8px 8px 0;
            transition: all 0.2s ease;
        }

        .action-item:hover {
            transform: translateX(5px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .action-item.urgent {
            border-left-color: #e74c3c;
            background: #fdf2f2;
        }

        .action-item.warning {
            border-left-color: #f39c12;
            background: #fdf8f2;
        }

        .action-item.success {
            border-left-color: #27ae60;
            background: #f2fdf5;
        }

        .action-info h4 {
            margin-bottom: 5px;
            color: #2c3e50;
        }

        .action-info p {
            font-size: 13px;
            color: #7f8c8d;
        }

        .action-button {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 600;
            transition: all 0.2s ease;
        }

        .btn-resolve {
            background: #e74c3c;
            color: white;
        }

        .btn-contest {
            background: #3498db;
            color: white;
        }

        .btn-details {
            background: #95a5a6;
            color: white;
        }

        .patterns-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .pattern-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }

        .pattern-card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 16px;
        }

        .vehicle-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #ecf0f1;
        }

        .vehicle-item:last-child {
            border-bottom: none;
        }

        .vehicle-info {
            display: flex;
            align-items: center;
        }

        .vehicle-rank {
            background: #3498db;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            margin-right: 10px;
        }

        .vehicle-details h4 {
            margin-bottom: 2px;
            color: #2c3e50;
        }

        .vehicle-details p {
            font-size: 12px;
            color: #7f8c8d;
        }

        .vehicle-stats {
            text-align: right;
        }

        .vehicle-stats .count {
            font-weight: bold;
            color: #e74c3c;
        }

        .vehicle-stats .value {
            font-size: 12px;
            color: #7f8c8d;
        }

        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .patterns-section {
                grid-template-columns: 1fr;
            }
            
            .metrics-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Dashboard Inteligente: O que Realmente Importa</h1>
        <p>Caça Multas - Revolução UX</p>
    </div>

    <div class="container">
        <!-- Cards de Métricas Principais -->
        <div class="metrics-grid">
            <div class="metric-card urgent">
                <div class="metric-icon">⚠️</div>
                <div class="metric-label">Atenção Imediata</div>
                <div class="metric-value">R$ 1.650</div>
                <div class="metric-detail">5 multas vencendo esta semana</div>
                <button class="metric-button urgent-btn">⚡ Resolver Agora</button>
            </div>

            <div class="metric-card economy">
                <div class="metric-icon">💰</div>
                <div class="metric-label">Economia Potencial</div>
                <div class="metric-value">R$ 3.200</div>
                <div class="metric-detail">25 multas contestáveis</div>
                <button class="metric-button economy-btn">💡 Contestar</button>
            </div>

            <div class="metric-card realized">
                <div class="metric-icon">📈</div>
                <div class="metric-label">Economia Realizada</div>
                <div class="metric-value">R$ 1.800</div>
                <div class="metric-detail">Último mês - +90%</div>
                <button class="metric-button">📊 Ver Detalhes</button>
            </div>

            <div class="metric-card prevented">
                <div class="metric-icon">🛡️</div>
                <div class="metric-label">Multas NIC Evitadas</div>
                <div class="metric-value">R$ 2.400</div>
                <div class="metric-detail">12 multas prevenidas</div>
                <button class="metric-button">👁️ Ver Motoristas</button>
            </div>
        </div>

        <!-- Gráficos Principais -->
        <div class="dashboard-grid">
            <div class="chart-container">
                <div class="chart-title">Tendência de Multas</div>
                <div style="position: relative; height: 300px;">
                    <canvas id="trendChart"></canvas>
                </div>
                <div style="background: #e8f4fd; padding: 10px; border-radius: 6px; margin-top: 10px; font-size: 12px;">
                    💡 <strong>Insight:</strong> Redução de 15% nas multas nos últimos 3 meses. Continue o bom trabalho!
                </div>
            </div>

            <div class="chart-container">
                <div class="chart-title">Multas por Órgão Autuador</div>
                <div style="position: relative; height: 300px;">
                    <canvas id="orgaoChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Ações Necessárias -->
        <div class="actions-section">
            <h2 style="margin-bottom: 20px; color: #2c3e50;">Ações Necessárias</h2>
            
            <div class="action-item urgent">
                <div class="action-info">
                    <h4>GAL9759</h4>
                    <p>Velocidade - R$ 130,16<br>📍 Multa no mesmo local</p>
                </div>
                <div>
                    <span style="color: #e74c3c; font-weight: bold; margin-right: 10px;">Vence hoje!</span>
                    <button class="action-button btn-resolve">Pagar</button>
                    <button class="action-button btn-contest">Contestar</button>
                </div>
            </div>

            <div class="action-item warning">
                <div class="action-info">
                    <h4>BVT4251</h4>
                    <p>Local Proibido - R$ 131,46</p>
                </div>
                <div>
                    <span style="color: #f39c12; font-weight: bold; margin-right: 10px;">2 dias</span>
                    <button class="action-button btn-resolve">Resolver</button>
                </div>
            </div>

            <div class="action-item success">
                <div class="action-info">
                    <h4>FXW0B43</h4>
                    <p>Velocidade - R$ 130,16<br>Pagamento confirmado</p>
                </div>
                <div>
                    <span style="color: #27ae60; font-weight: bold;">Resolvido</span>
                </div>
            </div>
        </div>

        <!-- Padrões Detectados e Top Veículos -->
        <div class="patterns-section">
            <div class="pattern-card">
                <h3>🔍 Padrões Detectados</h3>
                
                <div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin-bottom: 15px; border-left: 4px solid #ffc107;">
                    <h4 style="color: #856404; margin-bottom: 5px;">⚠️ Múltiplas multas - Mesmo local</h4>
                    <p style="color: #856404; font-size: 13px;">6 multas na Av. Alcântara Machado - Sempre às 13h</p>
                    <button style="background: #007bff; color: white; border: none; padding: 5px 10px; border-radius: 4px; font-size: 11px; margin-top: 8px;">Contactar um Lote</button>
                    <button style="background: #6c757d; color: white; border: none; padding: 5px 10px; border-radius: 4px; font-size: 11px; margin-top: 8px; margin-left: 5px;">Alterar Rota</button>
                </div>

                <div style="background: #d1ecf1; padding: 15px; border-radius: 8px; border-left: 4px solid #17a2b8;">
                    <h4 style="color: #0c5460; margin-bottom: 5px;">👨‍💼 Motorista com múltiplas infrações</h4>
                    <p style="color: #0c5460; font-size: 13px;">João Silva - 8 multas de velocidade em 30 dias</p>
                    <button style="background: #17a2b8; color: white; border: none; padding: 5px 10px; border-radius: 4px; font-size: 11px; margin-top: 8px;">Programa de Treinamento</button>
                    <button style="background: #6c757d; color: white; border: none; padding: 5px 10px; border-radius: 4px; font-size: 11px; margin-top: 8px; margin-left: 5px;">Ver Detalhes</button>
                </div>
            </div>

            <div class="pattern-card">
                <h3>🚛 Top Veículos</h3>
                
                <div class="vehicle-item">
                    <div class="vehicle-info">
                        <div class="vehicle-rank">1</div>
                        <div class="vehicle-details">
                            <h4>GAL9759</h4>
                            <p>13 multas - R$ 1.690</p>
                        </div>
                    </div>
                    <div class="vehicle-stats">
                        <button style="background: #007bff; color: white; border: none; padding: 4px 8px; border-radius: 4px; font-size: 11px;">Gerenciar</button>
                    </div>
                </div>

                <div class="vehicle-item">
                    <div class="vehicle-info">
                        <div class="vehicle-rank" style="background: #6c757d;">2</div>
                        <div class="vehicle-details">
                            <h4>BVT4251</h4>
                            <p>Gestor identifica rapidamente quais veículos precisam de atenção!</p>
                        </div>
                    </div>
                    <div class="vehicle-stats">
                        <div class="count">5 multas - R$ 650</div>
                        <button style="background: #007bff; color: white; border: none; padding: 4px 8px; border-radius: 4px; font-size: 11px; margin-top: 4px;">Gerenciar</button>
                    </div>
                </div>

                <div class="vehicle-item">
                    <div class="vehicle-info">
                        <div class="vehicle-rank" style="background: #dc3545;">3</div>
                        <div class="vehicle-details">
                            <h4>FXW0B43</h4>
                            <p>4 multas - R$ 520</p>
                        </div>
                    </div>
                    <div class="vehicle-stats">
                        <button style="background: #007bff; color: white; border: none; padding: 4px 8px; border-radius: 4px; font-size: 11px;">Gerenciar</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Gráfico de Tendências
        const trendCtx = document.getElementById('trendChart').getContext('2d');
        new Chart(trendCtx, {
            type: 'line',
            data: {
                labels: ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun'],
                datasets: [{
                    label: 'Valor (R$)',
                    data: [10000, 8500, 7200, 6800, 6200, 5400],
                    borderColor: '#e74c3c',
                    backgroundColor: 'rgba(231, 76, 60, 0.1)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: 'Quantidade',
                    data: [50, 42, 38, 35, 32, 28],
                    borderColor: '#3498db',
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    tension: 0.4,
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                    }
                }
            }
        });

        // Gráfico de Órgãos
        const orgaoCtx = document.getElementById('orgaoChart').getContext('2d');
        new Chart(orgaoCtx, {
            type: 'doughnut',
            data: {
                labels: ['DER/PR', 'PRF', 'DETRAN/PR', 'Prefeitura SP', 'Outros'],
                datasets: [{
                    data: [45, 25, 15, 10, 5],
                    backgroundColor: [
                        '#e74c3c',
                        '#3498db',
                        '#f39c12',
                        '#27ae60',
                        '#95a5a6'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                    }
                }
            }
        });
    </script>
</body>
</html>
