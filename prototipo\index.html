<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard CaçaMultas - EMPRESA TESTE PR</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f7fa;
            color: #333;
        }

        .header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-content {
            text-align: center;
            flex: 1;
        }

        .header-button {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .header-button:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
        }

        .analysis-section {
            margin: 40px 0;
            padding: 30px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 28px;
            margin-bottom: 5px;
        }

        .header p {
            opacity: 0.9;
            font-size: 16px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .metric-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            border-left: 5px solid;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            position: relative;
            overflow: hidden;
        }

        .metric-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.12);
        }

        .metric-card.urgent {
            border-left-color: #e74c3c;
            background: linear-gradient(135deg, #fff 0%, #fdf2f2 100%);
        }

        .metric-card.economy {
            border-left-color: #27ae60;
            background: linear-gradient(135deg, #fff 0%, #f2fdf5 100%);
        }

        .metric-card.success {
            border-left-color: #2ecc71;
            background: linear-gradient(135deg, #fff 0%, #f0fff4 100%);
        }

        .metric-card.realized {
            border-left-color: #3498db;
            background: linear-gradient(135deg, #fff 0%, #f2f8fd 100%);
        }

        .metric-card.prevented {
            border-left-color: #f39c12;
            background: linear-gradient(135deg, #fff 0%, #fdf8f2 100%);
        }

        .metric-icon {
            position: absolute;
            top: 20px;
            right: 20px;
            font-size: 24px;
            opacity: 0.3;
        }

        .metric-value {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 8px;
            color: #2c3e50;
        }

        .metric-label {
            font-size: 14px;
            color: #7f8c8d;
            margin-bottom: 15px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .metric-detail {
            font-size: 13px;
            color: #95a5a6;
        }

        .metric-button {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
            margin-top: 10px;
            transition: all 0.2s ease;
        }

        .metric-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
        }

        .metric-button.urgent-btn {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }

        .metric-button.economy-btn {
            background: linear-gradient(135deg, #27ae60, #229954);
        }

        .metric-button.success-btn {
            background: linear-gradient(135deg, #2ecc71, #27ae60);
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
            align-items: stretch;
        }

        .chart-container-box {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .chart-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .chart-canvas-wrapper {
            flex: 1;
            position: relative;
            min-height: 300px;
        }

        .chart-container {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }

        .chart-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #2c3e50;
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 10px;
        }

        .actions-section {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }

        .action-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            border-left: 4px solid;
            margin-bottom: 12px;
            border-radius: 0 8px 8px 0;
            transition: all 0.2s ease;
        }

        .action-item:hover {
            transform: translateX(5px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .action-item.urgent {
            border-left-color: #e74c3c;
            background: #fdf2f2;
        }

        .action-item.warning {
            border-left-color: #f39c12;
            background: #fdf8f2;
        }

        .action-item.success {
            border-left-color: #27ae60;
            background: #f2fdf5;
        }

        .action-info h4 {
            margin-bottom: 5px;
            color: #2c3e50;
        }

        .action-info p {
            font-size: 13px;
            color: #7f8c8d;
        }

        .action-button {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 600;
            transition: all 0.2s ease;
        }

        .btn-resolve {
            background: #e74c3c;
            color: white;
        }

        .btn-contest {
            background: #3498db;
            color: white;
        }

        .btn-details {
            background: #95a5a6;
            color: white;
        }



        .pattern-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }

        .pattern-card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 16px;
        }

        .vehicle-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #ecf0f1;
        }

        .vehicle-item:last-child {
            border-bottom: none;
        }

        .vehicle-info {
            display: flex;
            align-items: center;
        }

        .vehicle-rank {
            background: #3498db;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            margin-right: 10px;
        }

        .vehicle-details h4 {
            margin-bottom: 2px;
            color: #2c3e50;
        }

        .vehicle-details p {
            font-size: 12px;
            color: #7f8c8d;
        }

        .vehicle-stats {
            text-align: right;
        }

        .vehicle-stats .count {
            font-weight: bold;
            color: #e74c3c;
        }

        .vehicle-stats .value {
            font-size: 12px;
            color: #7f8c8d;
        }

        /* Análises Detalhadas */
        .detailed-analysis {
            margin: 40px 0;
            padding: 30px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .filters-section {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            flex-wrap: wrap;
            align-items: end;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .filter-group label {
            font-weight: 600;
            color: #2c3e50;
            font-size: 12px;
        }

        .filter-group select,
        .filter-group input[type="date"] {
            padding: 8px 12px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 13px;
            min-width: 180px;
            transition: border-color 0.3s ease;
        }

        .filter-group select:focus,
        .filter-group input[type="date"]:focus {
            outline: none;
            border-color: #3498db;
        }

        .filter-button {
            padding: 10px 20px;
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            height: fit-content;
        }

        .filter-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        }

        .chart-filters {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            align-items: end;
        }

        .chart-filter-button {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
        }

        .chart-filter-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
        }

        .analysis-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
        }

        .analysis-card {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }

        .analysis-card:hover {
            transform: translateY(-5px);
        }

        .analysis-card h3 {
            margin-bottom: 20px;
            color: #2c3e50;
            font-size: 16px;
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 10px;
        }

        .insight-box {
            margin-top: 15px;
            padding: 12px;
            background: linear-gradient(135deg, #e8f4fd, #d6eaf8);
            border-radius: 8px;
            border-left: 4px solid #3498db;
            font-size: 12px;
            color: #2c3e50;
        }

        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            

            
            .metrics-grid {
                grid-template-columns: 1fr;
            }

            .filters-section {
                flex-direction: column;
                gap: 15px;
            }

            .filter-group select,
            .filter-group input[type="date"] {
                min-width: 100%;
            }

            .chart-filters {
                flex-direction: column;
                gap: 10px;
            }

            .chart-filter-button {
                width: 100%;
            }



            .analysis-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div></div> <!-- Espaço vazio para centralizar o título -->
        <div class="header-content">
            <h1>🚛 Dashboard CaçaMultas - EMPRESA TESTE PR</h1>
        </div>
        <div>
            <button class="header-button" onclick="window.location.href='#multas'">
                📊 Ir para análise de multas
            </button>
        </div>
    </div>

    <div class="container">
        <!-- Cards de Métricas Principais -->
        <div class="metrics-grid">
            <div class="metric-card urgent">
                <div class="metric-icon">⚠️</div>
                <div class="metric-label">Multas Vencidas</div>
                <div class="metric-value">R$ 27.645,96</div>
                <div class="metric-detail">417 multas vencidas</div>
                <div class="metric-detail">12 vencem esta semana</div>
                <button class="metric-button urgent-btn">⚡ Resolver Agora</button>
            </div>

            <div class="metric-card urgent">
                <div class="metric-icon">⚠️</div>
                <div class="metric-label">Multas Em Aberto</div>
                <div class="metric-value">R$ 15.420,85</div>
                <div class="metric-detail">285 multas em aberto</div>
                <div class="metric-detail">Aguardando pagamento</div>
                <button class="metric-button urgent-btn">⚡ Resolver Agora</button>
            </div>

            <div class="metric-card success">
                <div class="metric-icon">✅</div>
                <div class="metric-label">Multas Pagas</div>
                <div class="metric-value">R$ 195,23</div>
                <div class="metric-detail">1 multa quitada</div>
                <div class="metric-detail">Histórico disponível</div>
                <button class="metric-button success-btn">📊 Ver Histórico</button>
            </div>

            <div class="metric-card prevented">
                <div class="metric-icon">🏢</div>
                <div class="metric-label">CNPJ Críticos</div>
                <div class="metric-value">3</div>
                <div class="metric-detail">Precisam atenção urgente</div>
                <div class="metric-detail">Análise necessária</div>
                <button class="metric-button">👁️ Ver CNPJs</button>
            </div>
        </div>

        <!-- Seção: Análise Temporal e Distribuição -->
        <div class="analysis-section">
            <h2 style="margin-bottom: 25px; color: #2c3e50; text-align: center;">📈 Análise Temporal e Distribuição</h2>

            <!-- Filtros dos Gráficos Principais -->
            <div style="display: flex; gap: 15px; margin-bottom: 25px; align-items: end;">
                <div class="filter-group">
                    <label>Data Inicial:</label>
                    <input type="date" id="mainChartDataInicial" value="2009-01-01">
                </div>
                <div class="filter-group">
                    <label>Data Final:</label>
                    <input type="date" id="mainChartDataFinal" value="2024-12-31">
                </div>
                <button class="chart-filter-button" onclick="aplicarFiltroGraficosPrincipais()">🔍 Aplicar Filtros</button>
            </div>

            <!-- Gráficos Principais -->
            <div class="dashboard-grid">
                <div class="chart-container-box">
                    <div class="chart-content">
                        <div class="chart-title">📊 Evolução Temporal</div>
                        <div class="chart-canvas-wrapper">
                            <canvas id="trendChart"></canvas>
                        </div>
                        <div style="background: #e8f4fd; padding: 10px; border-radius: 6px; margin-top: 10px; font-size: 12px;">
                            💡 <strong>Insight:</strong> Pico em 2011 com 170 multas. Redução drástica após 2013.
                        </div>
                    </div>
                </div>

                <div class="chart-container-box">
                    <div class="chart-content">
                        <div class="chart-title">Multas por Órgão Autuador</div>
                        <div class="chart-canvas-wrapper">
                            <canvas id="orgaoChart"></canvas>
                        </div>
                        <div style="background: #e8f4fd; padding: 10px; border-radius: 6px; margin-top: 10px; font-size: 12px;">
                            💡 <strong>Insight:</strong> DER/PR concentra 98% das multas aplicadas.
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Análises Detalhadas -->
        <div class="detailed-analysis">
            <h2 style="margin-bottom: 25px; color: #2c3e50; text-align: center;">📊 Análises Detalhadas</h2>

            <!-- Filtros -->
            <div class="filters-section">
                <div class="filter-group">
                    <label>Data Inicial:</label>
                    <input type="date" id="dataInicialFilter" value="2009-01-01">
                </div>
                <div class="filter-group">
                    <label>Data Final:</label>
                    <input type="date" id="dataFinalFilter" value="2024-12-31">
                </div>
                <div class="filter-group">
                    <label>Órgão Autuador:</label>
                    <select id="orgaoFilter">
                        <option value="todos">Todos</option>
                        <option value="DER/PR">DER/PR (412 multas)</option>
                        <option value="DNIT">DNIT (5 multas)</option>
                        <option value="DER/SP">DER/SP (1 multa)</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>CNPJ:</label>
                    <select id="cnpjFilter">
                        <option value="todos">Todos</option>
                        <option value="78015690000140">78.015.690/0001-40 (418 multas)</option>
                    </select>
                </div>
                <button class="filter-button">🔍 Aplicar Filtros</button>
            </div>

            <!-- Gráficos de Análise -->
            <div class="analysis-grid">

                <div class="analysis-card">
                    <h3>🏢 Top CNPJs</h3>
                    <div style="position: relative; height: 250px;">
                        <canvas id="cnpjChart"></canvas>
                    </div>
                    <div class="insight-box">
                        <strong>Insight:</strong> CNPJ 78.015.690/0001-40 concentra 418 multas (100% do total).
                    </div>
                </div>

                <div class="analysis-card">
                    <h3>📋 Motivos das Multas</h3>
                    <div style="position: relative; height: 250px;">
                        <canvas id="motivosChart"></canvas>
                    </div>
                    <div class="insight-box">
                        <strong>Insight:</strong> 98% das multas são por excesso de peso.
                    </div>
                </div>

                <div class="analysis-card">
                    <h3>📊 Status das Multas</h3>
                    <div style="position: relative; height: 250px;">
                        <canvas id="statusChart"></canvas>
                    </div>
                    <div class="insight-box">
                        <strong>Crítico:</strong> 99,8% das multas estão vencidas (417 de 418).
                    </div>
                </div>
            </div>
        </div>


    </div>

    <script>
        // Variáveis globais para os gráficos
        let trendChart, orgaoChart;

        // Gráfico de Evolução Temporal (Barras)
        const trendCtx = document.getElementById('trendChart').getContext('2d');
        trendChart = new Chart(trendCtx, {
            type: 'bar',
            data: {
                labels: ['2009', '2010', '2011', '2012', '2013', '2014', '2015-2024'],
                datasets: [{
                    label: 'Quantidade de Multas',
                    data: [15, 79, 170, 107, 25, 8, 14],
                    backgroundColor: [
                        '#3498db',
                        '#2980b9',
                        '#e74c3c',
                        '#c0392b',
                        '#f39c12',
                        '#e67e22',
                        '#95a5a6'
                    ],
                    borderColor: [
                        '#2980b9',
                        '#1f618d',
                        '#c0392b',
                        '#a93226',
                        '#e67e22',
                        '#d35400',
                        '#7f8c8d'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 20
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const value = context.parsed.y;
                                const percentage = ((value / total) * 100).toFixed(1);
                                const valorTotal = (value * 297).toLocaleString('pt-BR', {style: 'currency', currency: 'BRL'});
                                return [
                                    `Quantidade de multas: ${value}`,
                                    `Porcentagem: ${percentage}%`,
                                    `Valor total: ${valorTotal}`
                                ];
                            }
                        }
                    }
                }
            }
        });

        // Gráfico de Órgãos
        const orgaoCtx = document.getElementById('orgaoChart').getContext('2d');
        orgaoChart = new Chart(orgaoCtx, {
            type: 'doughnut',
            data: {
                labels: ['DER/PR', 'DNIT', 'DER/SP'],
                datasets: [{
                    data: [412, 5, 1],
                    backgroundColor: [
                        '#e74c3c',
                        '#3498db',
                        '#f39c12'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const value = context.parsed;
                                const percentage = ((value / total) * 100).toFixed(1);
                                const valorTotal = (value * 297).toLocaleString('pt-BR', {style: 'currency', currency: 'BRL'});
                                return [
                                    `Quantidade de multas: ${value}`,
                                    `Porcentagem: ${percentage}%`,
                                    `Valor total: ${valorTotal}`
                                ];
                            }
                        }
                    }
                }
            }
        });



        // Gráfico Top CNPJs
        const cnpjCtx = document.getElementById('cnpjChart').getContext('2d');
        new Chart(cnpjCtx, {
            type: 'bar',
            data: {
                labels: ['78.015.690/0001-40\nEMPRESA TESTE PR'],
                datasets: [{
                    label: 'Número de Multas por CNPJ',
                    data: [418],
                    backgroundColor: ['#e74c3c']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                indexAxis: 'y',
                plugins: {
                    legend: { display: false },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const value = context.parsed.x;
                                const percentage = "100.0"; // Como é só um CNPJ, sempre será 100%
                                const valorTotal = (value * 297).toLocaleString('pt-BR', {style: 'currency', currency: 'BRL'});
                                return [
                                    `Quantidade de multas: ${value}`,
                                    `Porcentagem: ${percentage}%`,
                                    `Valor total: ${valorTotal}`
                                ];
                            }
                        }
                    }
                },
                scales: {
                    x: { beginAtZero: true }
                }
            }
        });

        // Gráfico Motivos das Multas
        const motivosCtx = document.getElementById('motivosChart').getContext('2d');
        new Chart(motivosCtx, {
            type: 'doughnut',
            data: {
                labels: ['Excesso de Peso', 'Outras Infrações'],
                datasets: [{
                    data: [411, 7],
                    backgroundColor: ['#e74c3c', '#95a5a6'],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const value = context.parsed;
                                const percentage = ((value / total) * 100).toFixed(1);
                                const valorTotal = (value * 297).toLocaleString('pt-BR', {style: 'currency', currency: 'BRL'});
                                return [
                                    `Quantidade de multas: ${value}`,
                                    `Porcentagem: ${percentage}%`,
                                    `Valor total: ${valorTotal}`
                                ];
                            }
                        }
                    }
                }
            }
        });

        // Gráfico Status das Multas
        const statusCtx = document.getElementById('statusChart').getContext('2d');
        new Chart(statusCtx, {
            type: 'doughnut',
            data: {
                labels: ['Vencidas', 'Pagas'],
                datasets: [{
                    data: [417, 1],
                    backgroundColor: ['#e74c3c', '#27ae60'],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const value = context.parsed;
                                const percentage = ((value / total) * 100).toFixed(1);
                                const valorTotal = (value * 297).toLocaleString('pt-BR', {style: 'currency', currency: 'BRL'});
                                return [
                                    `Quantidade de multas: ${value}`,
                                    `Porcentagem: ${percentage}%`,
                                    `Valor total: ${valorTotal}`
                                ];
                            }
                        }
                    }
                }
            }
        });

        // Funcionalidade dos Filtros
        document.querySelector('.filter-button').addEventListener('click', function() {
            const dataInicial = document.getElementById('dataInicialFilter').value;
            const dataFinal = document.getElementById('dataFinalFilter').value;
            const orgao = document.getElementById('orgaoFilter').value;
            const cnpj = document.getElementById('cnpjFilter').value;

            // Simular aplicação de filtros
            alert(`Filtros aplicados:\nData Inicial: ${dataInicial}\nData Final: ${dataFinal}\nÓrgão Autuador: ${orgao}\nCNPJ: ${cnpj}`);
        });



        // Função para aplicar filtros nos gráficos principais
        function aplicarFiltroGraficosPrincipais() {
            const dataInicial = document.getElementById('mainChartDataInicial').value;
            const dataFinal = document.getElementById('mainChartDataFinal').value;

            // Converter datas para anos
            const anoInicial = new Date(dataInicial).getFullYear();
            const anoFinal = new Date(dataFinal).getFullYear();

            // Dados completos por ano
            const dadosCompletos = {
                2009: { multas: 15, orgaos: { 'DER/PR': 15, 'DNIT': 0, 'DER/SP': 0 } },
                2010: { multas: 79, orgaos: { 'DER/PR': 75, 'DNIT': 3, 'DER/SP': 1 } },
                2011: { multas: 170, orgaos: { 'DER/PR': 165, 'DNIT': 4, 'DER/SP': 1 } },
                2012: { multas: 107, orgaos: { 'DER/PR': 105, 'DNIT': 2, 'DER/SP': 0 } },
                2013: { multas: 25, orgaos: { 'DER/PR': 24, 'DNIT': 1, 'DER/SP': 0 } },
                2014: { multas: 8, orgaos: { 'DER/PR': 8, 'DNIT': 0, 'DER/SP': 0 } },
                2015: { multas: 3, orgaos: { 'DER/PR': 3, 'DNIT': 0, 'DER/SP': 0 } },
                2016: { multas: 2, orgaos: { 'DER/PR': 2, 'DNIT': 0, 'DER/SP': 0 } },
                2017: { multas: 1, orgaos: { 'DER/PR': 1, 'DNIT': 0, 'DER/SP': 0 } },
                2018: { multas: 1, orgaos: { 'DER/PR': 1, 'DNIT': 0, 'DER/SP': 0 } },
                2019: { multas: 1, orgaos: { 'DER/PR': 1, 'DNIT': 0, 'DER/SP': 0 } },
                2020: { multas: 1, orgaos: { 'DER/PR': 1, 'DNIT': 0, 'DER/SP': 0 } },
                2021: { multas: 1, orgaos: { 'DER/PR': 1, 'DNIT': 0, 'DER/SP': 0 } },
                2022: { multas: 1, orgaos: { 'DER/PR': 1, 'DNIT': 0, 'DER/SP': 0 } },
                2023: { multas: 2, orgaos: { 'DER/PR': 2, 'DNIT': 0, 'DER/SP': 0 } },
                2024: { multas: 1, orgaos: { 'DER/PR': 1, 'DNIT': 0, 'DER/SP': 0 } }
            };

            // Filtrar dados por período
            const anosFiltrados = [];
            const multasFiltradas = [];
            const coresBarras = [];
            let totalDerPr = 0, totalDnit = 0, totalDerSp = 0;

            for (let ano = anoInicial; ano <= anoFinal; ano++) {
                if (dadosCompletos[ano]) {
                    anosFiltrados.push(ano.toString());
                    multasFiltradas.push(dadosCompletos[ano].multas);

                    // Cores baseadas no valor
                    if (dadosCompletos[ano].multas >= 100) {
                        coresBarras.push('#e74c3c'); // Vermelho para valores altos
                    } else if (dadosCompletos[ano].multas >= 50) {
                        coresBarras.push('#f39c12'); // Laranja para valores médios
                    } else if (dadosCompletos[ano].multas >= 10) {
                        coresBarras.push('#3498db'); // Azul para valores baixos
                    } else {
                        coresBarras.push('#95a5a6'); // Cinza para valores muito baixos
                    }

                    // Somar totais por órgão
                    totalDerPr += dadosCompletos[ano].orgaos['DER/PR'];
                    totalDnit += dadosCompletos[ano].orgaos['DNIT'];
                    totalDerSp += dadosCompletos[ano].orgaos['DER/SP'];
                }
            }

            // Atualizar gráfico de Evolução Temporal
            trendChart.data.labels = anosFiltrados;
            trendChart.data.datasets[0].data = multasFiltradas;
            trendChart.data.datasets[0].backgroundColor = coresBarras;
            trendChart.update();

            // Atualizar gráfico de Órgãos
            orgaoChart.data.datasets[0].data = [totalDerPr, totalDnit, totalDerSp];
            orgaoChart.update();

            // Feedback visual
            const totalMultas = multasFiltradas.reduce((a, b) => a + b, 0);
            alert(`✅ Filtros aplicados com sucesso!\n\nPeríodo: ${anoInicial} - ${anoFinal}\nTotal de multas: ${totalMultas}\n\nDistribuição por órgão:\n• DER/PR: ${totalDerPr}\n• DNIT: ${totalDnit}\n• DER/SP: ${totalDerSp}`);
        }
    </script>
</body>
</html>
